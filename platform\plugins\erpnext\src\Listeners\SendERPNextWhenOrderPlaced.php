<?php

namespace Shaqi\ERPNext\Listeners;

use Exception;
use Bo<PERSON>ble\Ecommerce\Events\OrderCreated;
use Bo<PERSON>ble\Ecommerce\Events\OrderPlacedEvent;
use <PERSON>haqi\ERPNext\Services\ERPNextService;
use <PERSON>haqi\ERPNext\Services\ERPNextLogger;

class SendERPNextWhenOrderPlaced
{
    public function handle(OrderPlacedEvent|OrderCreated $event): void
    {
        $isERPNextEnabled = setting('enable_erpnext');

        if (! $isERPNextEnabled) {
            ERPNextLogger::debug("ERPNext integration is disabled");
            return;
        }

        try {
            $order = $event->order;

            ERPNextLogger::info("Processing order for ERPNext integration", [
                'order_id' => $order->id,
                'customer_email' => $order->shippingAddress->email ?? 'N/A',
                'order_amount' => $order->amount
            ]);

            // First, check if customer exists by email
            $customerID = ERPNextService::getCustomerByEmail($order->shippingAddress->email);

            // If customer still does not exist, create one
            if (!$customerID) {
                ERPNextLogger::info("Customer not found, creating new customer", [
                    'order_id' => $order->id,
                    'customer_email' => $order->shippingAddress->email
                ]);

                $customerID = ERPNextService::createCustomer(
                    $order->shippingAddress->name,
                    $order->shippingAddress->email,
                    $order->shippingAddress->phone
                );

                if (!$customerID) {
                    ERPNextLogger::error("Failed to create customer", [
                        'order_id' => $order->id,
                        'customer_email' => $order->shippingAddress->email
                    ]);
                    return;
                }
            }

            // Prepare items for sales order
            $items = [];
            foreach ($order->products as $product) {
                $items[] = [
                    "item_code" => $product['options']['sku'],
                    "qty" => $product['qty'],
                    "rate" => $product['price'],
                    "warehouse" => trim(setting('erpnext_warehouse')),
                ];
            }

            ERPNextLogger::info("Creating sales order", [
                'order_id' => $order->id,
                'customer_id' => $customerID,
                'items_count' => count($items)
            ]);

            $salesOrderResult = ERPNextService::createSalesOrder($customerID, $items, $order);

            // Handle the new return format properly
            if (is_array($salesOrderResult) && isset($salesOrderResult['success'])) {
                if ($salesOrderResult['success']) {
                    ERPNextLogger::info("Sales order created successfully", [
                        'order_id' => $order->id,
                        'customer_id' => $customerID,
                        'sales_order_id' => $salesOrderResult['sales_order_id']
                    ]);
                } else {
                    ERPNextLogger::error("Failed to create sales order", [
                        'order_id' => $order->id,
                        'customer_id' => $customerID,
                        'error' => $salesOrderResult['error'] ?? 'Unknown error'
                    ]);
                }
            } else {
                ERPNextLogger::error("Unexpected response format from createSalesOrder", [
                    'order_id' => $order->id,
                    'customer_id' => $customerID,
                    'response' => $salesOrderResult
                ]);
            }

            // $data = [
            //     'id' => $order->id,
            //     'status' => [
            //         'value' => $order->status->getValue(),
            //         'text' => $order->status->label(),
            //     ],
            //     'shipping_status' => $order->shipment->id ? [
            //         'value' => $order->shipment->status->getValue(),
            //         'text' => $order->shipment->status->label(),
            //     ] : [],
            //     'payment_method' => is_plugin_active('payment') && $order->payment->id ? [
            //         'value' => $order->payment->payment_channel->getValue(),
            //         'text' => $order->payment->payment_channel->label(),
            //     ] : [],
            //     'payment_status' => is_plugin_active('payment') && $order->payment->id ? [
            //         'value' => $order->payment->status->getValue(),
            //         'text' => $order->payment->status->label(),
            //     ] : [],
            //     'customer' => [
            //         'id' => $order->user->id,
            //         'name' => $order->user->name,
            //     ],
            //     'sub_total' => $order->sub_total,
            //     'tax_amount' => $order->tax_amount,
            //     'shipping_method' => $order->shipping_method->getValue(),
            //     'shipping_option' => $order->shipping_option,
            //     'shipping_amount' => $order->shipping_amount,
            //     'amount' => $order->amount,
            //     'coupon_code' => $order->coupon_code,
            //     'discount_amount' => $order->discount_amount,
            //     'discount_description' => $order->discount_description,
            //     'note' => $order->description,
            //     'is_confirmed' => $order->is_confirmed,
            // ];



        } catch (Exception $exception) {
            ERPNextLogger::error("Exception occurred during ERPNext order processing", [
                'order_id' => $order->id ?? 'unknown',
                'exception_message' => $exception->getMessage(),
                'exception_file' => $exception->getFile(),
                'exception_line' => $exception->getLine(),
                'stack_trace' => $exception->getTraceAsString()
            ]);
        }
    }
}
