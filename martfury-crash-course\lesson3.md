# Lesson 3: Plugin System Deep-dive

## Learning Objectives

By the end of this lesson, you will understand:
- Plugin architecture and directory structure
- How plugins integrate with the core system
- Marketplace and E-commerce plugin functionality
- Plugin service providers and registration
- Plugin routes, controllers, and models
- Plugin permissions and admin menus
- Plugin events and hooks system
- How to extend existing plugins

## 1. Plugin Architecture Overview

### What are Plugins in Botble CMS?

Plugins in Botble CMS are modular extensions that add specific functionality to the system. They follow a standardized structure and integrate seamlessly with the core framework.

### Key Plugin Characteristics

1. **Self-contained**: Each plugin contains all its resources (models, views, controllers, etc.)
2. **Configurable**: Plugins can be activated/deactivated without affecting core functionality
3. **Extensible**: Plugins can extend other plugins through hooks and events
4. **Standardized**: All plugins follow the same structural conventions

## 2. Plugin Directory Structure

### Standard Plugin Structure

```
platform/plugins/{plugin-name}/
├── config/                     # Plugin configuration files
│   ├── permissions.php        # Permission definitions
│   ├── general.php           # General settings
│   └── email.php             # Email templates
├── database/                  # Database related files
│   └── migrations/           # Database migrations
├── helpers/                   # Helper functions
├── public/                    # Public assets (CSS, JS, images)
├── resources/                 # Resources (views, lang, assets)
│   ├── views/               # Blade templates
│   ├── lang/                # Language files
│   └── email-templates/     # Email templates
├── routes/                    # Route definitions
│   ├── web.php             # Web routes
│   ├── api.php             # API routes
│   └── admin.php           # Admin routes
├── src/                       # PHP source code
│   ├── Http/               # Controllers and middleware
│   ├── Models/             # Eloquent models
│   ├── Providers/          # Service providers
│   ├── Repositories/       # Repository pattern classes
│   ├── Services/           # Business logic services
│   └── Plugin.php          # Plugin lifecycle class
├── plugin.json               # Plugin metadata
└── screenshot.png            # Plugin screenshot
```

## 3. Examining the Marketplace Plugin

### Task 3.1: Explore Marketplace Plugin Structure

1. Navigate to `platform/plugins/marketplace/` and examine the structure
2. Open `platform/plugins/marketplace/plugin.json`:

```json
{
    "id": "botble/marketplace",
    "name": "Marketplace",
    "namespace": "Botble\\Marketplace\\",
    "provider": "Botble\\Marketplace\\Providers\\MarketplaceServiceProvider",
    "author": "Botble technologies",
    "url": "https://botble.com",
    "version": "2.1.3",
    "description": "Marketplace plugin for Ecommerce site",
    "minimum_core_version": "7.3.0",
    "require": [
        "botble/ecommerce"
    ]
}
```

**Understanding**: This file defines plugin metadata, dependencies, and the service provider class.

### Task 3.2: Examine the Marketplace Service Provider

1. Open `platform/plugins/marketplace/src/Providers/MarketplaceServiceProvider.php`:

```php
public function boot(): void
{
    if (! is_plugin_active('ecommerce')) {
        return;
    }

    add_filter(IS_IN_ADMIN_FILTER, [$this, 'setInAdmin'], 128);

    $this
        ->setNamespace('plugins/marketplace')
        ->loadAndPublishConfigurations(['permissions', 'email', 'general'])
        ->loadMigrations()
        ->loadAndPublishTranslations()
        ->loadAndPublishViews()
        ->publishAssets()
        ->loadRoutes(['base', 'fronts', 'vendor']);
}
```

**Understanding**: The service provider loads plugin resources and checks dependencies.

## 4. Plugin Models and Relationships

### Task 3.3: Examine the Store Model

1. Open `platform/plugins/marketplace/src/Models/Store.php`:

```php
class Store extends BaseModel
{
    use LocationTrait;

    protected $table = 'mp_stores';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'country',
        'state',
        'city',
        'customer_id',
        'logo',
        'logo_square',
        'cover_image',
        'description',
        'content',
        'status',
        'company',
        'zip_code',
        'certificate_file',
        'government_id_file',
        'tax_id',
    ];
}
```

**Understanding**: The Store model represents vendor stores in the marketplace.

### Key Model Relationships

```php
// In Store model
public function customer(): BelongsTo
{
    return $this->belongsTo(Customer::class);
}

public function products(): HasMany
{
    return $this->hasMany(Product::class, 'store_id');
}

public function orders(): HasMany
{
    return $this->hasMany(Order::class, 'store_id');
}
```

## 5. Plugin Routes and Controllers

### Task 3.4: Examine Plugin Routes

1. Look at `platform/plugins/marketplace/routes/vendor.php`:

```php
Route::group([
    'namespace' => 'Botble\Marketplace\Http\Controllers\Fronts',
    'prefix' => config('plugins.marketplace.general.vendor_panel_dir', 'vendor'),
    'as' => 'marketplace.vendor.',
    'middleware' => ['web', 'core', 'vendor', LocaleMiddleware::class],
], function (): void {
    Route::get('dashboard', [
        'as' => 'dashboard',
        'uses' => 'DashboardController@index',
    ]);
    
    Route::get('products', [
        'as' => 'products.index',
        'uses' => 'ProductController@index',
    ]);
});
```

**Understanding**: Routes are organized by functionality and use middleware for access control.

### Route Organization

- `base.php`: Admin panel routes
- `fronts.php`: Public frontend routes
- `vendor.php`: Vendor dashboard routes
- `api.php`: API endpoints

## 6. Plugin Permissions and Admin Menus

### Task 3.5: Examine Permission Configuration

1. Open `platform/plugins/marketplace/config/permissions.php`:

```php
return [
    [
        'name' => 'Marketplace',
        'flag' => 'marketplace.index',
    ],
    [
        'name' => 'Stores',
        'flag' => 'marketplace.store.index',
        'parent_flag' => 'marketplace.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'marketplace.store.create',
        'parent_flag' => 'marketplace.store.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'marketplace.store.edit',
        'parent_flag' => 'marketplace.store.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'marketplace.store.destroy',
        'parent_flag' => 'marketplace.store.index',
    ],
];
```

### Task 3.6: Examine Admin Menu Registration

1. Look for menu registration in the service provider:

```php
DashboardMenu::default()->beforeRetrieving(function (): void {
    DashboardMenu::make()
        ->registerItem(
            DashboardMenuItem::make()
                ->id('cms-plugins-marketplace')
                ->priority(500)
                ->name('plugins/marketplace::marketplace.name')
                ->icon('ti ti-store')
                ->route('marketplace.index')
                ->permissions('marketplace.index')
        );
});
```

## 7. E-commerce Plugin Integration

### Task 3.7: Examine E-commerce Plugin Structure

1. Open `platform/plugins/ecommerce/plugin.json`:

```json
{
    "id": "botble/ecommerce",
    "name": "Ecommerce",
    "namespace": "Botble\\Ecommerce\\",
    "provider": "Botble\\Ecommerce\\Providers\\EcommerceServiceProvider",
    "author": "Botble Technologies",
    "url": "https://botble.com",
    "version": "3.2.2",
    "description": "Plugin for E-commerce features",
    "minimum_core_version": "7.3.0"
}
```

### E-commerce Service Provider

1. Examine `platform/plugins/ecommerce/src/Providers/EcommerceServiceProvider.php`:

```php
public function boot(): void
{
    $this
        ->loadAndPublishConfigurations(['permissions'])
        ->loadAndPublishTranslations()
        ->loadRoutes([
            'base',
            'product',
            'product-inventory',
            'product-price',
            'tax',
            'review',
            'shipping',
            'order',
            'discount',
            'customer',
            'cart',
            'shipment',
            'wishlist',
            'compare',
            'invoice',
            'setting',
            'product-specification',
            'api',
            'ajax',
        ])
        ->loadAndPublishConfigurations([
            'general',
            'shipping',
            'order',
            'cart',
            'email',
        ])
        ->loadAndPublishViews()
        ->loadMigrations()
        ->loadAnonymousComponents()
        ->publishAssets();
}
```

## 8. Plugin Configuration Files

### Task 3.8: Examine Plugin Configuration

1. Look at `platform/plugins/ecommerce/config/general.php`:

```php
return [
    'prefix' => 'ecommerce_',
    'display_big_money_in_million_billion' => env('DISPLAY_BIG_MONEY_IN_MILLION_BILLION', false),
    'bulk-import' => [
        'mime_types' => [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/csv',
            'application/csv',
            'text/plain',
        ],
        'mimes' => [
            'xls',
            'xlsx',
            'csv',
        ],
    ],
    'enable_faq_in_product_details' => true,
    'digital_products' => [
        'allowed_mime_types' => env('DIGITAL_PRODUCT_ALLOWED_MIME_TYPES', []),
    ],
];
```

## 9. Plugin Lifecycle and Hooks

### Plugin Lifecycle Methods

1. Examine `platform/plugins/ecommerce/src/Plugin.php`:

```php
class Plugin extends PluginOperationAbstract
{
    public static function activated(): void
    {
        Setting::set([
            'payment_cod_status' => 1,
            'payment_bank_transfer_status' => 1,
        ])->save();

        app('migrator')->run(database_path('migrations'));
    }

    public static function remove(): void
    {
        // Cleanup when plugin is removed
    }
}
```

### Plugin Hooks and Events

```php
// Adding hooks in service provider
add_filter('ecommerce_product_detail_extra_html', function ($html, $product) {
    return $html . '<div class="custom-content">Custom content</div>';
}, 10, 2);

// Listening to events
Event::listen('marketplace.store.created', function ($store) {
    // Handle store creation
});
```

## 10. Plugin Integration with Themes

### Task 3.9: Examine Theme Integration

1. Look at how plugins provide views that themes can override
2. Check `platform/plugins/ecommerce/resources/views/` structure
3. See how themes override these in `platform/themes/martfury/views/ecommerce/`

### View Override Hierarchy

1. **Theme Views**: `platform/themes/martfury/views/ecommerce/`
2. **Plugin Views**: `platform/plugins/ecommerce/resources/views/`
3. **Core Views**: `platform/core/*/resources/views/`

### Example: Product Detail View

```blade
{{-- Theme override: platform/themes/martfury/views/ecommerce/product.blade.php --}}
@extends(Theme::getThemeNamespace() . '::views.ecommerce.product')

{{-- Plugin view: platform/plugins/ecommerce/resources/views/product.blade.php --}}
<div class="product-detail">
    <!-- Product content -->
</div>
```

## 11. Creating Plugin Extensions

### Task 3.10: Extend Marketplace Functionality

1. Create a custom event listener for store creation:

```php
// In your theme's functions/functions.php
Event::listen('marketplace.store.created', function ($store) {
    // Send welcome email
    // Create default categories
    // Set up initial configuration
});
```

2. Add custom fields to store model:

```php
// In theme service provider
add_filter('marketplace_store_form_fields', function ($fields) {
    $fields['custom_field'] = [
        'type' => 'text',
        'label' => 'Custom Field',
        'required' => false,
    ];
    return $fields;
});
```

## 12. Plugin Helper Functions

### Common Plugin Helpers

```php
// Check if plugin is active
if (is_plugin_active('marketplace')) {
    // Marketplace functionality
}

// Get plugin configuration
$config = config('plugins.marketplace.general.vendor_panel_dir');

// Plugin path helpers
plugin_path('marketplace/src/Models/Store.php')
```

## 13. Practice Assignment

### Assignment 3.1: Analyze Plugin Dependencies

1. Map the dependency relationship between marketplace and ecommerce plugins
2. Identify which models and services are shared
3. Document the integration points

### Assignment 3.2: Explore Plugin Views

1. Navigate through the ecommerce plugin views
2. Find corresponding theme overrides in Martfury
3. Identify customization opportunities

### Assignment 3.3: Examine Plugin Routes

1. Map all routes in the marketplace plugin
2. Identify admin vs frontend vs vendor routes
3. Understand the middleware stack

## 14. Key Takeaways

1. **Modular Architecture**: Plugins are self-contained modules with standardized structure
2. **Service Providers**: Central point for plugin registration and configuration
3. **Dependencies**: Plugins can depend on other plugins (marketplace requires ecommerce)
4. **Permissions**: Granular permission system for access control
5. **View Hierarchy**: Themes can override plugin views
6. **Hooks and Events**: Extensibility through WordPress-style hooks and Laravel events
7. **Configuration**: Multiple configuration files for different aspects
8. **Lifecycle**: Plugins have activation, deactivation, and removal hooks

## 15. Next Steps

In Lesson 4, we'll explore routing and controller patterns in detail:
- Understanding route organization
- Controller structure and patterns
- Middleware implementation
- Request handling and validation
- Response formatting

## Quick Reference

### Plugin Structure Commands
```bash
# Create new plugin
php artisan cms:plugin:create <plugin-name>

# Activate plugin
php artisan cms:plugin:activate <plugin-name>

# Deactivate plugin
php artisan cms:plugin:deactivate <plugin-name>

# Remove plugin
php artisan cms:plugin:remove <plugin-name>
```

### Important Plugin Files
- `plugin.json`: Plugin metadata and dependencies
- `src/Providers/*ServiceProvider.php`: Main service provider
- `config/permissions.php`: Permission definitions
- `routes/*.php`: Route definitions
- `src/Plugin.php`: Lifecycle hooks
