# Lesson 6: Admin Panel Customization

## Learning Objectives

By the end of this lesson, you will understand:
- Dashboard menu system and customization
- Dashboard widgets creation and management
- Form builder system for admin interfaces
- Table builder system for data display
- Admin bar customization
- Panel sections and organization
- Custom admin controllers and views
- Admin assets and styling

## 1. Dashboard Menu System

### Understanding Menu Architecture

The dashboard menu system in Botble CMS consists of:
- **DashboardMenu**: Main class managing menu items and groups
- **DashboardMenuItem**: Builder class for creating menu items
- **Menu Groups**: Support for multiple menu contexts (admin, member, etc.)

### Task 6.1: Examine Menu Registration

1. Look at how core menus are registered in `platform/core/base/src/Providers/BaseServiceProvider.php`:

```php
protected function registerDashboardMenus(): void
{
    DashboardMenu::default()->beforeRetrieving(function (): void {
        DashboardMenu::make()
            ->registerItem(
                DashboardMenuItem::make()
                    ->id('cms-core-system')
                    ->priority(10000)
                    ->name('core/base::layouts.platform_admin')
                    ->icon('ti ti-user-shield')
                    ->route('system.index')
                    ->permission('core.system')
            )
            ->registerItem(
                DashboardMenuItem::make()
                    ->id('cms-core-tools')
                    ->priority(9000)
                    ->name('core/base::layouts.tools')
                    ->icon('ti ti-tool')
                    ->permission('core.tools')
            );
    });
}
```

### Task 6.2: Create Custom Menu Items

1. Add custom menu items in your plugin service provider:

```php
use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Supports\DashboardMenuItem;

DashboardMenu::default()->beforeRetrieving(function (): void {
    DashboardMenu::make()
        ->registerItem(
            DashboardMenuItem::make()
                ->id('cms-plugins-custom-reports')
                ->priority(500)
                ->parentId('cms-core-tools') // Make it a submenu
                ->name('Custom Reports')
                ->icon('ti ti-chart-line')
                ->route('custom-reports.index')
                ->permissions(['custom-reports.index'])
        )
        ->registerItem(
            DashboardMenuItem::make()
                ->id('cms-plugins-analytics')
                ->priority(600)
                ->name('Analytics Dashboard')
                ->icon('ti ti-analytics')
                ->route('analytics.dashboard')
                ->permissions(['analytics.view'])
        );
});
```

### Menu Item Properties

- `id()`: Unique identifier for the menu item
- `priority()`: Order in menu (lower numbers appear first)
- `parentId()`: Parent menu ID for submenus
- `name()`: Display name (can be translation key)
- `icon()`: Icon class (Tabler Icons)
- `route()`: Route name or URL
- `permissions()`: Required permissions array

## 2. Dashboard Widgets

### Understanding Widget Types

Botble CMS supports two widget types:
1. **Stats Widgets**: Small cards displaying single statistics
2. **Content Widgets**: Larger widgets with complex content

### Task 6.3: Create Stats Widget

1. Add a stats widget in your plugin service provider:

```php
add_filter(DASHBOARD_FILTER_ADMIN_LIST, function (array $widgets, Collection $widgetSettings) {
    return (new DashboardWidgetInstance())
        ->setType('stats')
        ->setPermission('product-reviews.index')
        ->setKey('widget_product_reviews_stats')
        ->setTitle('Total Reviews')
        ->setIcon('ti ti-star')
        ->setColor('primary')
        ->setStatsTotal(function () {
            return \Botble\ProductReviews\Models\ProductReview::count();
        })
        ->setRoute(route('product-reviews.index'))
        ->setColumn('col-12 col-md-6 col-lg-3')
        ->setPriority(10)
        ->init($widgets, $widgetSettings);
}, 99, 2);
```

### Task 6.4: Create Content Widget

1. Add a content widget for recent reviews:

```php
add_filter(DASHBOARD_FILTER_ADMIN_LIST, function (array $widgets, Collection $widgetSettings) {
    return (new DashboardWidgetInstance())
        ->setPermission('product-reviews.index')
        ->setKey('widget_recent_reviews')
        ->setTitle('Recent Product Reviews')
        ->setIcon('ti ti-message-star')
        ->setColor('success')
        ->setRoute(route('product-reviews.widget.recent'))
        ->setBodyClass('scroll-table')
        ->setColumn('col-md-6 col-sm-12')
        ->setPriority(20)
        ->init($widgets, $widgetSettings);
}, 99, 2);
```

2. Create widget controller method:

```php
public function getRecentReviewsWidget(Request $request)
{
    $reviews = $this->productReviewRepository
        ->getModel()
        ->with(['product', 'customer'])
        ->latest()
        ->limit(10)
        ->get();

    return view('plugins/product-reviews::widgets.recent-reviews', compact('reviews'));
}
```

3. Create widget view `platform/plugins/product-reviews/resources/views/widgets/recent-reviews.blade.php`:

```blade
<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Product</th>
                <th>Customer</th>
                <th>Rating</th>
                <th>Status</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            @foreach($reviews as $review)
            <tr>
                <td>{{ $review->product->name }}</td>
                <td>{{ $review->customer->name }}</td>
                <td>
                    @for($i = 1; $i <= 5; $i++)
                        <i class="ti ti-star{{ $i <= $review->rating ? '-filled' : '' }}"></i>
                    @endfor
                </td>
                <td>{!! $review->status->toHtml() !!}</td>
                <td>{{ $review->created_at->diffForHumans() }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
```

## 3. Form Builder System

### Understanding Form Builder

Botble CMS includes a powerful form builder that provides:
- Fluent API for form creation
- Built-in validation integration
- Multiple field types
- Responsive layouts
- Tab support

### Task 6.5: Create Custom Form

1. Create a form class `platform/plugins/product-reviews/src/Forms/ProductReviewForm.php`:

```php
<?php

namespace Botble\ProductReviews\Forms;

use Botble\Base\Forms\FieldOptions\EditorFieldOption;
use Botble\Base\Forms\FieldOptions\NumberFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\EditorField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Botble\ProductReviews\Enums\ReviewStatusEnum;
use Botble\ProductReviews\Http\Requests\ProductReviewRequest;
use Botble\ProductReviews\Models\ProductReview;

class ProductReviewForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->setupModel(new ProductReview())
            ->setValidatorClass(ProductReviewRequest::class)
            ->add('title', TextField::class, TextFieldOption::make()
                ->label('Review Title')
                ->required()
                ->maxLength(255)
            )
            ->add('content', EditorField::class, EditorFieldOption::make()
                ->label('Review Content')
                ->required()
            )
            ->add('rating', NumberField::class, NumberFieldOption::make()
                ->label('Rating')
                ->required()
                ->min(1)
                ->max(5)
                ->defaultValue(5)
            )
            ->add('status', SelectField::class, SelectFieldOption::make()
                ->label('Status')
                ->choices(ReviewStatusEnum::labels())
                ->required()
            )
            ->setBreakFieldPoint('status');
    }
}
```

### Form Field Types

Common field types include:
- `TextField`: Single-line text input
- `TextareaField`: Multi-line text input
- `EditorField`: Rich text editor
- `SelectField`: Dropdown selection
- `NumberField`: Numeric input
- `DateField`: Date picker
- `MediaImageField`: Image upload
- `CheckboxField`: Checkbox input

## 4. Table Builder System

### Understanding Table Builder

The table builder provides:
- DataTables integration
- Column definitions
- Actions and bulk actions
- Filtering and searching
- Export functionality

### Task 6.6: Create Custom Table

1. Create table class `platform/plugins/product-reviews/src/Tables/ProductReviewTable.php`:

```php
<?php

namespace Botble\ProductReviews\Tables;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\ProductReviews\Models\ProductReview;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class ProductReviewTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(ProductReview::class)
            ->addColumns([
                IdColumn::make(),
                Column::make('product.name')
                    ->title('Product')
                    ->searchable()
                    ->orderable(),
                Column::make('customer.name')
                    ->title('Customer')
                    ->searchable()
                    ->orderable(),
                Column::make('title')
                    ->title('Review Title')
                    ->searchable()
                    ->orderable(),
                Column::make('rating')
                    ->title('Rating')
                    ->orderable()
                    ->renderUsing(function (Column $column) {
                        $rating = $column->getItem()->rating;
                        $stars = '';
                        for ($i = 1; $i <= 5; $i++) {
                            $stars .= '<i class="ti ti-star' . ($i <= $rating ? '-filled text-warning' : '') . '"></i>';
                        }
                        return $stars;
                    }),
                StatusColumn::make(),
                CreatedAtColumn::make(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('product-reviews.create'))
            ->addActions([
                EditAction::make()->route('product-reviews.edit'),
                DeleteAction::make()->route('product-reviews.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('product-reviews.destroy'))
            ->addBulkChanges([
                StatusBulkChange::make()
                    ->name('status')
                    ->title('Status')
                    ->type('select')
                    ->choices(BaseStatusEnum::labels()),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'product_id',
                        'customer_id',
                        'title',
                        'rating',
                        'status',
                        'created_at',
                    ])
                    ->with(['product:id,name', 'customer:id,name']);
            });
    }
}
```

## 5. Admin Bar Customization

### Task 6.7: Add Admin Bar Links

1. Register admin bar links in your service provider:

```php
use Botble\Base\Events\RenderingAdminBar;

$this->app['events']->listen(RenderingAdminBar::class, function (): void {
    AdminBar::registerLink(
        'Product Reviews',
        route('product-reviews.index'),
        'appearance',
        'product-reviews.index'
    );
    
    AdminBar::registerLink(
        'Add Review',
        route('product-reviews.create'),
        'add-new',
        'product-reviews.create'
    );
});
```

## 6. Panel Sections

### Task 6.8: Create Custom Panel Section

1. Register panel sections for organized admin interface:

```php
use Botble\Base\Facades\PanelSectionManager;
use Botble\Base\PanelSections\PanelSectionItem;

PanelSectionManager::default()->beforeRendering(function (): void {
    PanelSectionManager::registerItem(
        'reviews',
        fn () => PanelSectionItem::make('reviews')
            ->setTitle('Review Management')
            ->withDescription('Manage product reviews and ratings')
            ->withPriority(100)
            ->withPermission('product-reviews.index')
    );
});
```

## 7. Practice Assignment

### Assignment 6.1: Create Custom Dashboard

1. Create a custom dashboard widget showing review statistics
2. Add menu items for review management
3. Implement custom admin bar links

### Assignment 6.2: Build Admin Interface

1. Create forms for review management
2. Build tables for listing reviews
3. Add bulk actions for review approval

### Assignment 6.3: Advanced Customization

1. Create custom admin theme modifications
2. Add JavaScript functionality to admin pages
3. Implement real-time notifications

## 8. Key Takeaways

1. **Menu System**: Use DashboardMenuItem for consistent menu registration
2. **Widgets**: Create both stats and content widgets for dashboard insights
3. **Forms**: Leverage form builder for consistent admin interfaces
4. **Tables**: Use table builder for powerful data management
5. **Admin Bar**: Provide quick access to common functions
6. **Panel Sections**: Organize admin interface logically
7. **Permissions**: Always implement proper permission checks

## 9. Next Steps

In Lesson 7, we'll explore Access Control Lists (ACL):
- User roles and permissions
- Security implementation
- Vendor access control
- Custom permission systems

## Quick Reference

### Menu Registration
```php
DashboardMenu::default()->beforeRetrieving(function (): void {
    DashboardMenu::make()->registerItem(
        DashboardMenuItem::make()
            ->id('unique-id')
            ->name('Menu Name')
            ->route('route.name')
            ->permissions(['permission'])
    );
});
```

### Widget Registration
```php
add_filter(DASHBOARD_FILTER_ADMIN_LIST, function ($widgets, $widgetSettings) {
    return (new DashboardWidgetInstance())
        ->setKey('widget_key')
        ->setTitle('Widget Title')
        ->init($widgets, $widgetSettings);
}, 99, 2);
```
