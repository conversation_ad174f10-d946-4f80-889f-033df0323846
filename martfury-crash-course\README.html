<!DOCTYPE html>
<html>
<head>
<title>README.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/*
Monokai style - ported by Luigi Maselli - http://grigio.org
*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #272822;
  color: #ddd;
}

.hljs-tag,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-strong,
.hljs-name {
  color: #f92672;
}

.hljs-code {
  color: #66d9ef;
}

.hljs-class .hljs-title {
  color: white;
}

.hljs-attribute,
.hljs-symbol,
.hljs-regexp,
.hljs-link {
  color: #bf79db;
}

.hljs-string,
.hljs-bullet,
.hljs-subst,
.hljs-title,
.hljs-section,
.hljs-emphasis,
.hljs-type,
.hljs-built_in,
.hljs-builtin-name,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #a6e22e;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  color: #75715e;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-selector-id {
  font-weight: bold;
}

</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="martfury-multi-vendor-marketplace-crash-course">Martfury Multi-Vendor Marketplace Crash Course</h1>
<p>Welcome to the comprehensive 10-lesson crash course for the Martfury Multi-Vendor Marketplace built on Botble CMS. This course is designed for Laravel beginners with basic PHP knowledge who want to master the Martfury marketplace system.</p>
<h2 id="course-overview">Course Overview</h2>
<p>This crash course covers everything you need to know to customize, extend, and maintain the Martfury Multi-Vendor Marketplace. Each lesson builds upon the previous one, providing hands-on experience with real code examples from the actual codebase.</p>
<h2 id="prerequisites">Prerequisites</h2>
<ul>
<li>Basic PHP knowledge</li>
<li>Familiarity with HTML/CSS</li>
<li>Understanding of MVC concepts</li>
<li>Local development environment (Laragon, XAMPP, or similar)</li>
</ul>
<h2 id="course-structure">Course Structure</h2>
<h3 id="lesson-1-botble-cms-fundamentals-and-martfury-architecture">Lesson 1: Botble CMS Fundamentals and Martfury Architecture</h3>
<ul>
<li><strong>Duration</strong>: 2-3 hours</li>
<li><strong>Focus</strong>: Understanding the foundation of Botble CMS and Martfury theme structure</li>
<li><strong>Key Topics</strong>: Directory structure, modular architecture, theme basics</li>
</ul>
<h3 id="lesson-2-theme-customization-essentials">Lesson 2: Theme Customization Essentials</h3>
<ul>
<li><strong>Duration</strong>: 2-3 hours</li>
<li><strong>Focus</strong>: Blade templates, assets, and navigation customization</li>
<li><strong>Key Topics</strong>: Template hierarchy, asset compilation, menu systems</li>
</ul>
<h3 id="lesson-3-plugin-system-deep-dive">Lesson 3: Plugin System Deep-dive</h3>
<ul>
<li><strong>Duration</strong>: 3-4 hours</li>
<li><strong>Focus</strong>: Understanding plugin architecture and marketplace functionality</li>
<li><strong>Key Topics</strong>: Plugin structure, marketplace logic, vendor management</li>
</ul>
<h3 id="lesson-4-routing-and-controller-patterns">Lesson 4: Routing and Controller Patterns</h3>
<ul>
<li><strong>Duration</strong>: 2-3 hours</li>
<li><strong>Focus</strong>: URL routing, controller implementation, and request handling</li>
<li><strong>Key Topics</strong>: Route definitions, controller methods, middleware</li>
</ul>
<h3 id="lesson-5-custom-plugin-development">Lesson 5: Custom Plugin Development</h3>
<ul>
<li><strong>Duration</strong>: 4-5 hours</li>
<li><strong>Focus</strong>: Building a complete plugin from scratch</li>
<li><strong>Key Topics</strong>: Plugin creation, CRUD operations, service providers</li>
</ul>
<h3 id="lesson-6-admin-panel-customization">Lesson 6: Admin Panel Customization</h3>
<ul>
<li><strong>Duration</strong>: 3-4 hours</li>
<li><strong>Focus</strong>: Modifying the admin interface and dashboard</li>
<li><strong>Key Topics</strong>: Admin menus, widgets, custom sections</li>
</ul>
<h3 id="lesson-7-access-control-lists-acl">Lesson 7: Access Control Lists (ACL)</h3>
<ul>
<li><strong>Duration</strong>: 3-4 hours</li>
<li><strong>Focus</strong>: User roles, permissions, and security</li>
<li><strong>Key Topics</strong>: Role management, permission systems, vendor access</li>
</ul>
<h3 id="lesson-8-data-layer-management">Lesson 8: Data Layer Management</h3>
<ul>
<li><strong>Duration</strong>: 3-4 hours</li>
<li><strong>Focus</strong>: Models, repositories, and database interactions</li>
<li><strong>Key Topics</strong>: Eloquent models, repository pattern, migrations</li>
</ul>
<h3 id="lesson-9-advanced-customization-case-study">Lesson 9: Advanced Customization Case Study</h3>
<ul>
<li><strong>Duration</strong>: 4-5 hours</li>
<li><strong>Focus</strong>: Vendor dashboard modifications and marketplace features</li>
<li><strong>Key Topics</strong>: UI customization, vendor-specific functionality</li>
</ul>
<h3 id="lesson-10-capstone-project">Lesson 10: Capstone Project</h3>
<ul>
<li><strong>Duration</strong>: 5-6 hours</li>
<li><strong>Focus</strong>: Building a complete reviews/ratings module</li>
<li><strong>Key Topics</strong>: Full-stack development, integration, testing</li>
</ul>
<h2 id="learning-objectives">Learning Objectives</h2>
<p>By the end of this course, you will be able to:</p>
<ol>
<li>Navigate and understand the Botble CMS architecture</li>
<li>Customize Martfury theme templates and styling</li>
<li>Develop custom plugins for marketplace functionality</li>
<li>Modify routing and controller logic</li>
<li>Implement custom admin panel features</li>
<li>Manage user roles and permissions effectively</li>
<li>Work with the data layer and database relationships</li>
<li>Build complete marketplace features from scratch</li>
</ol>
<h2 id="getting-started">Getting Started</h2>
<ol>
<li>Ensure your Martfury installation is working properly</li>
<li>Set up your development environment</li>
<li>Familiarize yourself with the project structure</li>
<li>Start with Lesson 1 and progress sequentially</li>
</ol>
<h2 id="support-and-resources">Support and Resources</h2>
<ul>
<li><strong>Official Botble Documentation</strong>: https://docs.botble.com/</li>
<li><strong>Laravel Documentation</strong>: https://laravel.com/docs</li>
<li><strong>Martfury Theme Files</strong>: <code>platform/themes/martfury/</code></li>
<li><strong>Plugin Examples</strong>: <code>platform/plugins/</code></li>
</ul>
<h2 id="course-files">Course Files</h2>
<p>Each lesson includes:</p>
<ul>
<li>Detailed explanations and concepts</li>
<li>Hands-on tasks with specific file paths</li>
<li>Code examples with syntax highlighting</li>
<li>Practice assignments to reinforce learning</li>
</ul>
<p>Let's begin your journey to mastering the Martfury Multi-Vendor Marketplace!</p>

</body>
</html>
