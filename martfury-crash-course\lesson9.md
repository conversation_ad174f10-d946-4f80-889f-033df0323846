# Lesson 9: Advanced Customization Case Study

## Learning Objectives

By the end of this lesson, you will be able to:
- Customize vendor dashboard UI and functionality
- Implement advanced marketplace features
- Modify vendor-specific controllers and views
- Add custom vendor dashboard widgets
- Enhance vendor product management
- Implement vendor analytics and reporting
- Customize vendor authentication flow
- Create vendor-specific middleware and security

## 1. Case Study Overview

### Project: Enhanced Vendor Dashboard

We'll create a comprehensive vendor dashboard enhancement that includes:
- Custom dashboard widgets with real-time data
- Advanced product management features
- Enhanced order processing workflow
- Vendor analytics and reporting
- Custom notification system
- Mobile-responsive design improvements

### Current Vendor Dashboard Structure

The vendor dashboard is located at:
- **Routes**: `platform/plugins/marketplace/routes/vendor.php`
- **Controllers**: `platform/plugins/marketplace/src/Http/Controllers/Fronts/`
- **Views**: `platform/plugins/marketplace/resources/views/themes/vendor-dashboard/`
- **Assets**: `platform/plugins/marketplace/public/`

## 2. Analyzing Current Vendor Dashboard

### Task 9.1: Examine Vendor Routes

1. Look at `platform/plugins/marketplace/routes/vendor.php`:

```php
Route::group([
    'namespace' => 'Botble\Marketplace\Http\Controllers\Fronts',
    'prefix' => config('plugins.marketplace.general.vendor_panel_dir', 'vendor'),
    'as' => 'marketplace.vendor.',
    'middleware' => ['web', 'core', 'vendor', LocaleMiddleware::class],
], function (): void {
    Route::get('dashboard', [
        'as' => 'dashboard',
        'uses' => 'DashboardController@index',
    ]);

    Route::resource('products', 'ProductController');
    Route::resource('orders', 'OrderController');
    Route::resource('revenues', 'RevenueController');
    Route::resource('withdrawals', 'WithdrawalController');
    
    // AJAX routes for charts and data
    Route::group(['prefix' => 'ajax', 'as' => 'ajax.'], function (): void {
        Route::get('chart/month', [
            'as' => 'chart.month',
            'uses' => 'RevenueController@getMonthChart',
        ]);
    });
});
```

### Task 9.2: Examine Dashboard Controller

1. Look at `platform/plugins/marketplace/src/Http/Controllers/Fronts/DashboardController.php`:

```php
class DashboardController extends BaseController
{
    public function __construct()
    {
        $version = get_cms_version();

        Theme::asset()
            ->add('customer-style', 'vendor/core/plugins/ecommerce/css/customer.css', ['bootstrap-css'], version: $version);

        Theme::asset()
            ->container('footer')
            ->add('ecommerce-utilities-js', 'vendor/core/plugins/ecommerce/js/utilities.js', ['jquery'], version: $version)
            ->add('cropper-js', 'vendor/core/plugins/ecommerce/libraries/cropper.js', ['jquery'], version: $version)
            ->add('avatar-js', 'vendor/core/plugins/ecommerce/js/avatar.js', ['jquery'], version: $version);
    }

    public function index(Request $request)
    {
        $this->pageTitle(__('Dashboard'));

        Assets::addScriptsDirectly([
            'vendor/core/plugins/ecommerce/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/ecommerce/libraries/apexcharts-bundle/dist/apexcharts.min.js',
            'vendor/core/plugins/ecommerce/js/report.js',
        ]);

        // Dashboard data logic...
        $user = auth('customer')->user();
        $store = $user->store;
        
        return MarketplaceHelper::view('vendor-dashboard.index', compact('user', 'store'));
    }
}
```

## 3. Creating Enhanced Dashboard Widgets

### Task 9.3: Create Advanced Analytics Widget

1. Create custom dashboard controller extension:

```php
<?php

namespace App\Http\Controllers\Vendor;

use Botble\Marketplace\Http\Controllers\Fronts\DashboardController as BaseDashboardController;
use Botble\Ecommerce\Models\Order;
use Botble\Ecommerce\Models\Product;
use Botble\Marketplace\Models\Revenue;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EnhancedDashboardController extends BaseDashboardController
{
    public function index(Request $request)
    {
        $this->pageTitle(__('Enhanced Dashboard'));

        // Load additional assets for enhanced features
        Assets::addScriptsDirectly([
            'vendor/core/plugins/ecommerce/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/ecommerce/libraries/apexcharts-bundle/dist/apexcharts.min.js',
            'vendor/core/plugins/ecommerce/js/report.js',
            'assets/js/vendor-dashboard-enhanced.js', // Custom JS
        ]);

        Assets::addStylesDirectly([
            'assets/css/vendor-dashboard-enhanced.css', // Custom CSS
        ]);

        $user = auth('customer')->user();
        $store = $user->store;

        // Enhanced analytics data
        $analytics = $this->getEnhancedAnalytics($store->id);
        $recentActivity = $this->getRecentActivity($store->id);
        $performanceMetrics = $this->getPerformanceMetrics($store->id);
        $topProducts = $this->getTopProducts($store->id);

        return MarketplaceHelper::view('vendor-dashboard.enhanced-index', compact(
            'user',
            'store',
            'analytics',
            'recentActivity',
            'performanceMetrics',
            'topProducts'
        ));
    }

    protected function getEnhancedAnalytics(int $storeId): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'sales_today' => $this->getSalesForPeriod($storeId, $today, $today),
            'sales_yesterday' => $this->getSalesForPeriod($storeId, $yesterday, $yesterday),
            'sales_this_month' => $this->getSalesForPeriod($storeId, $thisMonth, $today),
            'sales_last_month' => $this->getSalesForPeriod($storeId, $lastMonth, $lastMonth->endOfMonth()),
            'orders_today' => $this->getOrdersForPeriod($storeId, $today, $today),
            'orders_this_month' => $this->getOrdersForPeriod($storeId, $thisMonth, $today),
            'conversion_rate' => $this->getConversionRate($storeId),
            'average_order_value' => $this->getAverageOrderValue($storeId),
        ];
    }

    protected function getSalesForPeriod(int $storeId, Carbon $start, Carbon $end): float
    {
        return Revenue::where('customer_id', auth('customer')->id())
            ->whereBetween('created_at', [$start, $end->endOfDay()])
            ->sum('amount');
    }

    protected function getOrdersForPeriod(int $storeId, Carbon $start, Carbon $end): int
    {
        return Order::where('store_id', $storeId)
            ->whereBetween('created_at', [$start, $end->endOfDay()])
            ->count();
    }

    protected function getConversionRate(int $storeId): float
    {
        $totalViews = Product::where('store_id', $storeId)->sum('views');
        $totalOrders = Order::where('store_id', $storeId)->count();

        return $totalViews > 0 ? round(($totalOrders / $totalViews) * 100, 2) : 0;
    }

    protected function getAverageOrderValue(int $storeId): float
    {
        return Order::where('store_id', $storeId)
            ->avg('amount') ?: 0;
    }

    protected function getRecentActivity(int $storeId): array
    {
        $recentOrders = Order::where('store_id', $storeId)
            ->with(['customer:id,name', 'products:id,name'])
            ->latest()
            ->limit(5)
            ->get();

        $recentProducts = Product::where('store_id', $storeId)
            ->latest()
            ->limit(5)
            ->get(['id', 'name', 'created_at', 'status']);

        return [
            'orders' => $recentOrders,
            'products' => $recentProducts,
        ];
    }

    protected function getPerformanceMetrics(int $storeId): array
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        return [
            'product_views' => Product::where('store_id', $storeId)
                ->where('updated_at', '>=', $thirtyDaysAgo)
                ->sum('views'),
            'total_products' => Product::where('store_id', $storeId)->count(),
            'published_products' => Product::where('store_id', $storeId)
                ->where('status', 'published')
                ->count(),
            'out_of_stock' => Product::where('store_id', $storeId)
                ->where('quantity', '<=', 0)
                ->count(),
        ];
    }

    protected function getTopProducts(int $storeId): array
    {
        return Product::where('store_id', $storeId)
            ->withCount(['orderProducts as sales_count'])
            ->orderByDesc('sales_count')
            ->limit(10)
            ->get(['id', 'name', 'image', 'price']);
    }
}
```

### Task 9.4: Create Enhanced Dashboard View

1. Create `platform/themes/martfury/views/marketplace/vendor-dashboard/enhanced-index.blade.php`:

```blade
@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="enhanced-vendor-dashboard">
        <!-- Quick Stats Row -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ format_price($analytics['sales_today']) }}</h4>
                                <p class="mb-0">{{ __('Sales Today') }}</p>
                            </div>
                            <div class="stat-icon">
                                <i class="ti ti-currency-dollar"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            @php
                                $change = $analytics['sales_yesterday'] > 0 
                                    ? (($analytics['sales_today'] - $analytics['sales_yesterday']) / $analytics['sales_yesterday']) * 100 
                                    : 0;
                            @endphp
                            <small class="d-flex align-items-center">
                                <i class="ti ti-trending-{{ $change >= 0 ? 'up' : 'down' }} me-1"></i>
                                {{ number_format(abs($change), 1) }}% {{ __('from yesterday') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $analytics['orders_today'] }}</h4>
                                <p class="mb-0">{{ __('Orders Today') }}</p>
                            </div>
                            <div class="stat-icon">
                                <i class="ti ti-shopping-cart"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small>{{ $analytics['orders_this_month'] }} {{ __('this month') }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $analytics['conversion_rate'] }}%</h4>
                                <p class="mb-0">{{ __('Conversion Rate') }}</p>
                            </div>
                            <div class="stat-icon">
                                <i class="ti ti-target"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small>{{ __('Views to orders ratio') }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ format_price($analytics['average_order_value']) }}</h4>
                                <p class="mb-0">{{ __('Avg Order Value') }}</p>
                            </div>
                            <div class="stat-icon">
                                <i class="ti ti-receipt"></i>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small>{{ __('Per order average') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ __('Sales Analytics') }}</h5>
                        <div class="card-actions">
                            <div class="dropdown">
                                <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    {{ __('Last 30 Days') }}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-period="7">{{ __('Last 7 Days') }}</a></li>
                                    <li><a class="dropdown-item" href="#" data-period="30">{{ __('Last 30 Days') }}</a></li>
                                    <li><a class="dropdown-item" href="#" data-period="90">{{ __('Last 90 Days') }}</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="sales-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ __('Performance Metrics') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="metric-item">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>{{ __('Total Products') }}</span>
                                <strong>{{ $performanceMetrics['total_products'] }}</strong>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                <div class="progress-bar bg-primary" style="width: 100%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>{{ __('Published') }}</span>
                                <strong>{{ $performanceMetrics['published_products'] }}</strong>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                @php
                                    $publishedPercentage = $performanceMetrics['total_products'] > 0 
                                        ? ($performanceMetrics['published_products'] / $performanceMetrics['total_products']) * 100 
                                        : 0;
                                @endphp
                                <div class="progress-bar bg-success" style="width: {{ $publishedPercentage }}%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>{{ __('Out of Stock') }}</span>
                                <strong class="text-danger">{{ $performanceMetrics['out_of_stock'] }}</strong>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                @php
                                    $outOfStockPercentage = $performanceMetrics['total_products'] > 0 
                                        ? ($performanceMetrics['out_of_stock'] / $performanceMetrics['total_products']) * 100 
                                        : 0;
                                @endphp
                                <div class="progress-bar bg-danger" style="width: {{ $outOfStockPercentage }}%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>{{ __('Total Views (30d)') }}</span>
                                <strong>{{ number_format($performanceMetrics['product_views']) }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Top Products -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ __('Recent Orders') }}</h5>
                        <a href="{{ route('marketplace.vendor.orders.index') }}" class="btn btn-outline-primary btn-sm">
                            {{ __('View All') }}
                        </a>
                    </div>
                    <div class="card-body">
                        @if($recentActivity['orders']->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Order') }}</th>
                                            <th>{{ __('Customer') }}</th>
                                            <th>{{ __('Amount') }}</th>
                                            <th>{{ __('Date') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($recentActivity['orders'] as $order)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('marketplace.vendor.orders.edit', $order->id) }}">
                                                        #{{ $order->code }}
                                                    </a>
                                                </td>
                                                <td>{{ $order->customer->name ?? __('Guest') }}</td>
                                                <td>{{ format_price($order->amount) }}</td>
                                                <td>{{ $order->created_at->diffForHumans() }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="ti ti-shopping-cart-off text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">{{ __('No recent orders') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">{{ __('Top Selling Products') }}</h5>
                        <a href="{{ route('marketplace.vendor.products.index') }}" class="btn btn-outline-primary btn-sm">
                            {{ __('Manage Products') }}
                        </a>
                    </div>
                    <div class="card-body">
                        @if($topProducts->count() > 0)
                            @foreach($topProducts as $product)
                                <div class="d-flex align-items-center mb-3">
                                    <img src="{{ RvMedia::getImageUrl($product->image, 'thumb') }}" 
                                         alt="{{ $product->name }}" 
                                         class="rounded me-3" 
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ Str::limit($product->name, 30) }}</h6>
                                        <small class="text-muted">{{ $product->sales_count }} {{ __('sales') }}</small>
                                    </div>
                                    <div class="text-end">
                                        <strong>{{ format_price($product->price) }}</strong>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-4">
                                <i class="ti ti-package text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">{{ __('No products yet') }}</p>
                                <a href="{{ route('marketplace.vendor.products.create') }}" class="btn btn-primary btn-sm">
                                    {{ __('Add Product') }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    <script>
        // Enhanced dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sales chart
            if (document.getElementById('sales-chart')) {
                initializeSalesChart();
            }

            // Period selector functionality
            document.querySelectorAll('[data-period]').forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    const period = this.dataset.period;
                    updateChartData(period);
                });
            });
        });

        function initializeSalesChart() {
            // ApexCharts configuration for sales analytics
            const options = {
                series: [{
                    name: '{{ __("Sales") }}',
                    data: [] // Will be populated via AJAX
                }],
                chart: {
                    type: 'area',
                    height: 300,
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                xaxis: {
                    type: 'datetime'
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return '{{ get_application_currency()->symbol }}' + value.toFixed(0);
                        }
                    }
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                    }
                },
                colors: ['#007bff']
            };

            const chart = new ApexCharts(document.querySelector("#sales-chart"), options);
            chart.render();

            // Load initial data
            loadChartData(30);
        }

        function loadChartData(period) {
            fetch(`{{ route('marketplace.vendor.ajax.chart.month') }}?period=${period}`)
                .then(response => response.json())
                .then(data => {
                    // Update chart with new data
                    ApexCharts.exec('sales-chart', 'updateSeries', [{
                        name: '{{ __("Sales") }}',
                        data: data.series
                    }]);
                })
                .catch(error => {
                    console.error('Error loading chart data:', error);
                });
        }

        function updateChartData(period) {
            loadChartData(period);
        }
    </script>
@endpush
```

## 4. Custom CSS for Enhanced Dashboard

### Task 9.5: Create Enhanced Dashboard Styles

1. Create `public/assets/css/vendor-dashboard-enhanced.css`:

```css
/* Enhanced Vendor Dashboard Styles */
.enhanced-vendor-dashboard {
    padding: 20px 0;
}

.stat-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.metric-item {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
    border-bottom: none;
}

.progress {
    background-color: rgba(255, 255, 255, 0.2);
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid #f0f0f0;
    padding: 1.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
    color: #2c3e50;
}

.card-actions {
    margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .enhanced-vendor-dashboard {
        padding: 10px 0;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-icon {
        font-size: 2rem;
    }
    
    .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .card-actions {
        margin-left: 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2c3e50;
        color: #ecf0f1;
    }
    
    .card-header {
        border-bottom-color: #34495e;
    }
    
    .metric-item {
        border-bottom-color: #34495e;
    }
    
    .text-muted {
        color: #95a5a6 !important;
    }
}
```

## 5. Practice Assignment

### Assignment 9.1: Implement Real-time Notifications

1. Create a real-time notification system for vendors
2. Add WebSocket integration for live updates
3. Implement notification preferences

### Assignment 9.2: Advanced Product Management

1. Add bulk product operations
2. Implement advanced filtering and search
3. Create product performance analytics

### Assignment 9.3: Mobile Dashboard Optimization

1. Create mobile-specific dashboard views
2. Implement touch-friendly interactions
3. Add offline capability for basic functions

## 6. Key Takeaways

1. **Modular Enhancement**: Build on existing structure rather than replacing
2. **Performance Focus**: Use caching and efficient queries for analytics
3. **User Experience**: Prioritize intuitive design and responsive layout
4. **Real-time Data**: Implement live updates for critical metrics
5. **Mobile First**: Ensure dashboard works well on all devices
6. **Security**: Maintain vendor isolation and proper permissions
7. **Extensibility**: Design for future enhancements and customizations

## 7. Next Steps

In Lesson 10, we'll build a complete capstone project:
- Reviews/ratings module from scratch
- Full integration with marketplace
- Advanced features and customization
- Testing and deployment

## Quick Reference

### Vendor Dashboard Structure
```
vendor-dashboard/
├── layouts/
│   ├── master.blade.php
│   ├── header.blade.php
│   └── sidebar.blade.php
├── partials/
│   └── dashboard-content.blade.php
├── index.blade.php
└── enhanced-index.blade.php
```

### Key Routes
```php
// Vendor dashboard
Route::get('dashboard', 'DashboardController@index');

// AJAX endpoints
Route::get('ajax/chart/month', 'RevenueController@getMonthChart');
```
