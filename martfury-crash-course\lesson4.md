# Lesson 4: Routing and Controller Patterns

## Learning Objectives

By the end of this lesson, you will understand:
- Route organization and structure in Botble CMS
- Controller inheritance hierarchy and patterns
- Middleware implementation and usage
- Request handling and validation
- Response formatting and view rendering
- Theme-specific routing
- API routing patterns
- Vendor dashboard routing

## 1. Route Organization Overview

### Route Structure in Botble CMS

Botble CMS organizes routes across multiple layers:

1. **Core Routes**: `platform/core/*/routes/`
2. **Package Routes**: `platform/packages/*/routes/`
3. **Plugin Routes**: `platform/plugins/*/routes/`
4. **Theme Routes**: `platform/themes/*/routes/`
5. **Application Routes**: `routes/`

### Route Loading Order

Routes are loaded in this order:
1. Core and package routes (automatically loaded by service providers)
2. Plugin routes (loaded by plugin service providers)
3. Theme routes (loaded when theme is active)
4. Application routes (loaded by Lara<PERSON>)

## 2. Theme Routing in Martfury

### Task 4.1: Examine Martfury Theme Routes

1. Open `platform/themes/martfury/routes/web.php`:

```php
<?php

use Botble\Theme\Facades\Theme;
use Illuminate\Support\Facades\Route;
use Theme\Martfury\Http\Controllers\MartfuryController;

Theme::registerRoutes(function (): void {
    Route::group(['controller' => MartfuryController::class], function (): void {
        Route::group(['prefix' => 'ajax', 'as' => 'public.ajax.'], function (): void {
            Route::get('products', 'ajaxGetProducts')
                ->name('products');

            Route::get('cart', 'ajaxCart')
                ->name('cart');

            Route::get('search-products', 'ajaxSearchProducts')
                ->name('search-products');

            Route::post('send-download-app-links', 'ajaxSendDownloadAppLinks')
                ->name('send-download-app-links');

            Route::get('products-by-collection/{id}', 'ajaxGetProductsByCollection')
                ->name('products-by-collection')
                ->wherePrimaryKey();

            Route::get('products-by-category/{id}', 'ajaxGetProductsByCategory')
                ->name('products-by-category')
                ->wherePrimaryKey();
        });
    });
});

Theme::routes();
```

**Understanding**: 
- `Theme::registerRoutes()` is the recommended way to register theme routes
- Routes are grouped by functionality (AJAX routes in this case)
- `Theme::routes()` loads default theme routes

### Theme Route Registration Methods

#### Method 1: Theme::registerRoutes() (Recommended)

```php
Theme::registerRoutes(function (): void {
    Route::group(['controller' => YourController::class], function (): void {
        Route::get('custom-page', 'getCustomPage')->name('public.custom-page');
    });
});
```

**Benefits**:
- Automatically applies `web` and `core` middleware
- Applies `BASE_FILTER_GROUP_PUBLIC_ROUTE` filter
- Cleaner syntax
- Better theme integration

#### Method 2: Traditional Laravel Groups (Legacy)

```php
Route::group(['namespace' => 'Theme\YourTheme\Http\Controllers', 'middleware' => 'web'], function () {
    Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {
        Route::get('hello', 'YourThemeController@getHello');
    });
});
```

## 3. Controller Patterns and Inheritance

### Task 4.2: Examine Controller Hierarchy

1. Look at the base controller structure:

```php
// platform/core/base/src/Http/Controllers/BaseController.php
class BaseController extends Controller
{
    use HasBreadcrumb;
    use HasHttpResponse;
    use HasPageTitle;
    use AuthorizesRequests;
    use DispatchesJobs;
    use ValidatesRequests;
}
```

2. Examine the Martfury controller:

```php
// platform/themes/martfury/src/Http/Controllers/MartfuryController.php
class MartfuryController extends PublicController
{
    public function __construct(protected BaseHttpResponse $httpResponse)
    {
        $this->middleware(function ($request, $next) {
            if (! $request->ajax()) {
                return $this->httpResponse->setNextUrl(route('public.index'));
            }
            return $next($request);
        })->only([
            'ajaxGetProducts',
            'ajaxCart',
            'ajaxSearchProducts',
            // ... other AJAX methods
        ]);
    }
}
```

### Controller Inheritance Hierarchy

```
Controller (Laravel)
└── BaseController (Botble Core)
    ├── BaseSystemController (System controllers)
    ├── PublicController (Theme controllers)
    └── Plugin Controllers
        ├── DashboardController (Marketplace vendor)
        └── Other plugin controllers
```

### Task 4.3: Examine Marketplace Vendor Controller

1. Open `platform/plugins/marketplace/src/Http/Controllers/Fronts/DashboardController.php`:

```php
class DashboardController extends BaseController
{
    public function __construct()
    {
        $version = get_cms_version();

        Theme::asset()
            ->add('customer-style', 'vendor/core/plugins/ecommerce/css/customer.css', ['bootstrap-css'], version: $version);

        Theme::asset()
            ->container('footer')
            ->add('ecommerce-utilities-js', 'vendor/core/plugins/ecommerce/js/utilities.js', ['jquery'], version: $version)
            ->add('cropper-js', 'vendor/core/plugins/ecommerce/libraries/cropper.js', ['jquery'], version: $version)
            ->add('avatar-js', 'vendor/core/plugins/ecommerce/js/avatar.js', ['jquery'], version: $version);
    }

    public function index(Request $request)
    {
        $this->pageTitle(__('Dashboard'));

        Assets::addScriptsDirectly([
            'vendor/core/plugins/ecommerce/libraries/daterangepicker/daterangepicker.js',
            'vendor/core/plugins/ecommerce/libraries/apexcharts-bundle/dist/apexcharts.min.js',
            'vendor/core/plugins/ecommerce/js/report.js',
        ]);
        
        // Dashboard logic...
    }
}
```

**Understanding**: Controllers can load assets, set page titles, and handle complex business logic.

## 4. Plugin Route Organization

### Task 4.4: Examine Plugin Route Structure

1. Look at marketplace plugin routes in `platform/plugins/marketplace/routes/`:

```
routes/
├── base.php          # Admin panel routes
├── fronts.php        # Public frontend routes  
├── vendor.php        # Vendor dashboard routes
└── language-advanced.php # Language-specific routes
```

2. Examine vendor routes structure:

```php
// platform/plugins/marketplace/routes/vendor.php
Route::group([
    'namespace' => 'Botble\Marketplace\Http\Controllers\Fronts',
    'prefix' => config('plugins.marketplace.general.vendor_panel_dir', 'vendor'),
    'as' => 'marketplace.vendor.',
    'middleware' => ['web', 'core', 'vendor', LocaleMiddleware::class],
], function (): void {
    Route::get('dashboard', [
        'as' => 'dashboard',
        'uses' => 'DashboardController@index',
    ]);
    
    Route::get('products', [
        'as' => 'products.index',
        'uses' => 'ProductController@index',
    ]);
});
```

### Route Organization Patterns

#### Admin Routes Pattern

```php
Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {
    Route::group(['prefix' => 'plugin-name', 'as' => 'plugin-name.'], function () {
        Route::resource('', 'ItemController')->parameters(['' => 'item']);
        
        Route::delete('items/destroy', [
            'as' => 'deletes',
            'uses' => 'ItemController@deletes',
            'permission' => 'plugin-name.destroy',
        ]);
    });
});
```

#### API Routes Pattern

```php
Route::group([
    'prefix' => 'api/v1',
    'namespace' => 'Botble\Plugin\Http\Controllers\API',
    'middleware' => ['api'],
], function () {
    Route::get('items', [
        'as' => 'api.plugin.index',
        'uses' => 'ItemController@index',
    ]);
});
```

## 5. Middleware Implementation

### Task 4.5: Examine Middleware Usage

1. Look at common middleware patterns:

```php
// AJAX-only middleware
$this->middleware(function ($request, $next) {
    if (! $request->ajax()) {
        return $this->httpResponse->setNextUrl(route('public.index'));
    }
    return $next($request);
})->only(['ajaxMethod1', 'ajaxMethod2']);

// Authentication middleware
Route::group(['middleware' => 'auth'], function () {
    // Protected routes
});

// Custom middleware
Route::group(['middleware' => ['web', 'core', 'vendor']], function () {
    // Vendor-specific routes
});
```

### Common Middleware in Botble CMS

- `web`: Laravel's web middleware group
- `core`: Botble core middleware
- `auth`: Authentication required
- `vendor`: Marketplace vendor authentication
- `RequiresJsonRequestMiddleware`: AJAX requests only

## 6. Request Handling and Validation

### Task 4.6: Examine Request Handling

1. Look at request validation patterns:

```php
// Custom request class
class SendDownloadAppLinksRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'phone' => 'required|string',
        ];
    }
}

// Controller usage
public function ajaxSendDownloadAppLinks(SendDownloadAppLinksRequest $request)
{
    $validated = $request->validated();
    // Process request...
}
```

2. Inline validation:

```php
public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users',
    ]);
    
    // Process validated data...
}
```

## 7. Response Formatting

### Task 4.7: Examine Response Patterns

1. JSON responses for AJAX:

```php
public function ajaxGetProducts(Request $request)
{
    $products = $this->getProducts($request);
    
    return $this->httpResponse->setData([
        'data' => Theme::partial('product-items', compact('products')),
        'message' => __('Products loaded successfully'),
    ]);
}
```

2. View responses:

```php
public function index()
{
    $this->pageTitle(__('Dashboard'));
    
    return Theme::scope('marketplace.dashboard', [
        'user' => auth()->user(),
        'stats' => $this->getDashboardStats(),
    ])->render();
}
```

3. Redirect responses:

```php
public function store(Request $request)
{
    // Process data...
    
    return $this->httpResponse
        ->setNextUrl(route('admin.items.index'))
        ->withCreatedSuccessMessage();
}
```

## 8. Route Model Binding

### Task 4.8: Examine Model Binding

1. Automatic binding:

```php
Route::get('products/{product}', 'ProductController@show');

// Controller method
public function show(Product $product)
{
    return view('product.show', compact('product'));
}
```

2. Custom binding with constraints:

```php
Route::get('products/{id}', 'ProductController@show')
    ->where('id', '[0-9]+')
    ->name('public.product');

// Or using wherePrimaryKey() helper
Route::get('products/{id}', 'ProductController@show')
    ->wherePrimaryKey();
```

## 9. API Routing Patterns

### Task 4.9: Examine API Routes

1. RESTful API structure:

```php
Route::group([
    'prefix' => 'api/v1',
    'middleware' => ['api', 'auth:sanctum'],
], function () {
    Route::apiResource('products', ProductController::class);
    Route::apiResource('orders', OrderController::class);
});
```

2. Custom API endpoints:

```php
Route::group(['prefix' => 'api/v1/marketplace'], function () {
    Route::get('stores/{store}/products', 'StoreController@products');
    Route::post('stores/{store}/follow', 'StoreController@follow');
});
```

## 10. Route Caching and Optimization

### Route Caching Commands

```bash
# Cache routes for production
php artisan route:cache

# Clear route cache
php artisan route:clear

# List all routes
php artisan route:list

# List routes for specific middleware
php artisan route:list --middleware=auth
```

### Performance Considerations

1. Use route caching in production
2. Group routes efficiently
3. Use route model binding for cleaner code
4. Implement proper middleware stacking

## 11. Practice Assignment

### Assignment 4.1: Create Custom Theme Routes

1. Add a custom route to Martfury theme:
   - Create route for `/custom-products`
   - Add controller method
   - Create corresponding view

2. Implement AJAX route:
   - Add AJAX route for product filtering
   - Handle JSON response
   - Add proper middleware

### Assignment 4.2: Analyze Route Structure

1. Map all routes in marketplace plugin
2. Identify route groups and their purposes
3. Document middleware usage patterns

### Assignment 4.3: Create Custom Controller

1. Create a custom controller extending PublicController
2. Implement proper request validation
3. Add response formatting
4. Register routes properly

## 12. Key Takeaways

1. **Route Organization**: Routes are organized by functionality and access level
2. **Controller Hierarchy**: Clear inheritance pattern with shared functionality
3. **Middleware**: Proper middleware stacking for security and functionality
4. **Request Handling**: Use form requests for complex validation
5. **Response Patterns**: Consistent response formatting for different contexts
6. **Theme Integration**: Theme routes integrate seamlessly with core system
7. **API Design**: RESTful patterns with proper versioning

## 13. Next Steps

In Lesson 5, we'll dive into custom plugin development:
- Creating a plugin from scratch
- Implementing CRUD operations
- Adding admin interface
- Integrating with existing plugins

## Quick Reference

### Route Registration Methods
```php
// Theme routes (recommended)
Theme::registerRoutes(function (): void {
    Route::get('path', 'Controller@method');
});

// Plugin routes
Route::group(['middleware' => ['web', 'core']], function () {
    Route::resource('items', 'ItemController');
});
```

### Controller Patterns
```php
// Base controller usage
class MyController extends BaseController
{
    public function index()
    {
        $this->pageTitle('Page Title');
        return view('my-view');
    }
}
```

### Response Patterns
```php
// JSON response
return $this->httpResponse->setData($data);

// View response
return Theme::scope('view-name', $data)->render();

// Redirect response
return $this->httpResponse->setNextUrl($url);
```
