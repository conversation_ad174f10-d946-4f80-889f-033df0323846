# Lesson 5: Custom Plugin Development

## Learning Objectives

By the end of this lesson, you will be able to:
- Create a custom plugin from scratch
- Implement CRUD operations with proper structure
- Set up database migrations and models
- Create admin interface with forms and tables
- Implement proper permissions and security
- Integrate with existing Botble CMS features
- Follow best practices for plugin development

## 1. Plugin Development Overview

### What We'll Build

In this lesson, we'll create a **Product Reviews** plugin that allows customers to leave reviews for products in the Martfury marketplace. This plugin will demonstrate:

- Complete CRUD operations
- Database relationships
- Admin interface
- Frontend integration
- Permission system
- Email notifications

### Prerequisites

Before starting, ensure you have:
- Development environment set up
- Composer dependencies installed
- Basic understanding of Laravel and Botble CMS

## 2. Creating the Plugin Structure

### Task 5.1: Generate Plugin Skeleton

1. Run the plugin creation command:

```bash
php artisan cms:plugin:create product-reviews
```

This generates the complete plugin structure in `platform/plugins/product-reviews/`.

2. Examine the generated structure:

```
platform/plugins/product-reviews/
├── config/
│   └── permissions.php
├── database/
│   └── migrations/
├── resources/
│   ├── lang/
│   └── views/
├── routes/
│   └── web.php
├── src/
│   ├── Http/
│   │   └── Controllers/
│   ├── Models/
│   ├── Providers/
│   │   └── ProductReviewsServiceProvider.php
│   └── Plugin.php
├── plugin.json
└── screenshot.png
```

### Task 5.2: Configure Plugin Metadata

1. Edit `platform/plugins/product-reviews/plugin.json`:

```json
{
    "id": "botble/product-reviews",
    "name": "Product Reviews",
    "namespace": "Botble\\ProductReviews\\",
    "provider": "Botble\\ProductReviews\\Providers\\ProductReviewsServiceProvider",
    "author": "Your Name",
    "url": "https://yourwebsite.com",
    "version": "1.0.0",
    "description": "Allow customers to leave reviews for products",
    "minimum_core_version": "7.3.0",
    "require": [
        "botble/ecommerce"
    ]
}
```

**Understanding**: The plugin depends on the ecommerce plugin and follows semantic versioning.

## 3. Database Design and Migrations

### Task 5.3: Create Database Migration

1. Create migration file `platform/plugins/product-reviews/database/migrations/2024_01_01_000001_create_product_reviews_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->string('title');
            $table->text('content');
            $table->tinyInteger('rating')->unsigned()->default(5);
            $table->string('status', 60)->default('pending');
            $table->json('images')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->index(['product_id', 'status']);
            $table->index(['customer_id', 'status']);
            $table->unique(['product_id', 'customer_id']); // One review per customer per product
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_reviews');
    }
};
```

### Task 5.4: Create Review Model

1. Create `platform/plugins/product-reviews/src/Models/ProductReview.php`:

```php
<?php

namespace Botble\ProductReviews\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Botble\Ecommerce\Models\Product;
use Botble\ProductReviews\Enums\ReviewStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ProductReview extends BaseModel
{
    protected $table = 'product_reviews';

    protected $fillable = [
        'product_id',
        'customer_id',
        'title',
        'content',
        'rating',
        'status',
        'images',
        'approved_at',
        'approved_by',
    ];

    protected $casts = [
        'status' => ReviewStatusEnum::class,
        'title' => SafeContent::class,
        'content' => SafeContent::class,
        'images' => 'array',
        'approved_at' => 'datetime',
        'rating' => 'integer',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\Botble\ACL\Models\User::class, 'approved_by');
    }

    protected function averageRating(): Attribute
    {
        return Attribute::make(
            get: fn () => number_format($this->rating, 1)
        );
    }

    public function scopeApproved($query)
    {
        return $query->where('status', ReviewStatusEnum::APPROVED);
    }

    public function scopePending($query)
    {
        return $query->where('status', ReviewStatusEnum::PENDING);
    }
}
```

### Task 5.5: Create Review Status Enum

1. Create `platform/plugins/product-reviews/src/Enums/ReviewStatusEnum.php`:

```php
<?php

namespace Botble\ProductReviews\Enums;

use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static ReviewStatusEnum PENDING()
 * @method static ReviewStatusEnum APPROVED()
 * @method static ReviewStatusEnum REJECTED()
 */
class ReviewStatusEnum extends Enum
{
    public const PENDING = 'pending';
    public const APPROVED = 'approved';
    public const REJECTED = 'rejected';

    public static function labels(): array
    {
        return [
            self::PENDING => __('Pending'),
            self::APPROVED => __('Approved'),
            self::REJECTED => __('Rejected'),
        ];
    }

    public static function colors(): array
    {
        return [
            self::PENDING => 'warning',
            self::APPROVED => 'success',
            self::REJECTED => 'danger',
        ];
    }

    public function toHtml(): HtmlString|string
    {
        $color = self::colors()[$this->value] ?? 'secondary';
        
        return new HtmlString(sprintf(
            '<span class="badge bg-%s">%s</span>',
            $color,
            self::labels()[$this->value] ?? $this->value
        ));
    }
}
```

## 4. Service Provider Configuration

### Task 5.6: Configure Service Provider

1. Edit `platform/plugins/product-reviews/src/Providers/ProductReviewsServiceProvider.php`:

```php
<?php

namespace Botble\ProductReviews\Providers;

use Botble\Base\Facades\DashboardMenu;
use Botble\Base\Supports\DashboardMenuItem;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\ProductReviews\Models\ProductReview;
use Botble\ProductReviews\Repositories\Interfaces\ProductReviewInterface;
use Botble\ProductReviews\Repositories\ProductReviewRepository;
use Illuminate\Support\ServiceProvider;

class ProductReviewsServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(ProductReviewInterface::class, function () {
            return new ProductReviewRepository(new ProductReview());
        });
    }

    public function boot(): void
    {
        if (! is_plugin_active('ecommerce')) {
            return;
        }

        $this
            ->setNamespace('plugins/product-reviews')
            ->loadAndPublishConfigurations(['permissions'])
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-product-reviews')
                        ->priority(520)
                        ->name('plugins/product-reviews::product-reviews.name')
                        ->icon('ti ti-star')
                        ->route('product-reviews.index')
                        ->permissions('product-reviews.index')
                );
        });

        $this->app->booted(function (): void {
            $this->registerHooks();
        });
    }

    protected function registerHooks(): void
    {
        // Add hooks for product page integration
        add_filter('ecommerce_product_detail_extra_html', function ($html, $product) {
            if (! $product) {
                return $html;
            }

            $reviews = app(ProductReviewInterface::class)
                ->getModel()
                ->where('product_id', $product->id)
                ->approved()
                ->with('customer')
                ->latest()
                ->paginate(10);

            return $html . view('plugins/product-reviews::frontend.product-reviews', compact('product', 'reviews'))->render();
        }, 10, 2);
    }
}
```

## 5. Repository Pattern Implementation

### Task 5.7: Create Repository Interface and Implementation

1. Create `platform/plugins/product-reviews/src/Repositories/Interfaces/ProductReviewInterface.php`:

```php
<?php

namespace Botble\ProductReviews\Repositories\Interfaces;

use Botble\Support\Repositories\Interfaces\RepositoryInterface;

interface ProductReviewInterface extends RepositoryInterface
{
    public function getReviewsByProduct(int $productId, int $limit = 10);
    
    public function getAverageRating(int $productId): float;
    
    public function getTotalReviews(int $productId): int;
    
    public function getReviewsByCustomer(int $customerId, int $limit = 10);
}
```

2. Create `platform/plugins/product-reviews/src/Repositories/ProductReviewRepository.php`:

```php
<?php

namespace Botble\ProductReviews\Repositories;

use Botble\ProductReviews\Repositories\Interfaces\ProductReviewInterface;
use Botble\Support\Repositories\Eloquent\RepositoriesAbstract;

class ProductReviewRepository extends RepositoriesAbstract implements ProductReviewInterface
{
    public function getReviewsByProduct(int $productId, int $limit = 10)
    {
        return $this->model
            ->where('product_id', $productId)
            ->approved()
            ->with('customer')
            ->latest()
            ->paginate($limit);
    }

    public function getAverageRating(int $productId): float
    {
        return (float) $this->model
            ->where('product_id', $productId)
            ->approved()
            ->avg('rating') ?: 0;
    }

    public function getTotalReviews(int $productId): int
    {
        return $this->model
            ->where('product_id', $productId)
            ->approved()
            ->count();
    }

    public function getReviewsByCustomer(int $customerId, int $limit = 10)
    {
        return $this->model
            ->where('customer_id', $customerId)
            ->with('product')
            ->latest()
            ->paginate($limit);
    }
}
```

## 6. Admin Controller Implementation

### Task 5.8: Create Admin Controller

1. Create `platform/plugins/product-reviews/src/Http/Controllers/ProductReviewController.php`:

```php
<?php

namespace Botble\ProductReviews\Http\Controllers;

use Botble\Base\Events\BeforeEditContentEvent;
use Botble\Base\Events\CreatedContentEvent;
use Botble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Botble\Base\Facades\PageTitle;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\ProductReviews\Forms\ProductReviewForm;
use Botble\ProductReviews\Http\Requests\ProductReviewRequest;
use Botble\ProductReviews\Models\ProductReview;
use Botble\ProductReviews\Repositories\Interfaces\ProductReviewInterface;
use Botble\ProductReviews\Tables\ProductReviewTable;
use Exception;
use Illuminate\Http\Request;

class ProductReviewController extends BaseController
{
    public function __construct(protected ProductReviewInterface $productReviewRepository)
    {
    }

    public function index(ProductReviewTable $table)
    {
        PageTitle::setTitle(trans('plugins/product-reviews::product-reviews.name'));

        return $table->renderTable();
    }

    public function create()
    {
        PageTitle::setTitle(trans('plugins/product-reviews::product-reviews.create'));

        return ProductReviewForm::create()->renderForm();
    }

    public function store(ProductReviewRequest $request, BaseHttpResponse $response)
    {
        $productReview = $this->productReviewRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(PRODUCT_REVIEW_MODULE_SCREEN_NAME, $request, $productReview));

        return $response
            ->setPreviousUrl(route('product-reviews.index'))
            ->setNextUrl(route('product-reviews.edit', $productReview->id))
            ->withCreatedSuccessMessage();
    }

    public function show(int $id)
    {
        $productReview = $this->productReviewRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $productReview));

        PageTitle::setTitle(trans('plugins/product-reviews::product-reviews.edit') . ' "' . $productReview->title . '"');

        return ProductReviewForm::createFromModel($productReview)->renderForm();
    }

    public function edit(int $id, Request $request)
    {
        $productReview = $this->productReviewRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $productReview));

        PageTitle::setTitle(trans('plugins/product-reviews::product-reviews.edit') . ' "' . $productReview->title . '"');

        return ProductReviewForm::createFromModel($productReview)->renderForm();
    }

    public function update(int $id, ProductReviewRequest $request, BaseHttpResponse $response)
    {
        $productReview = $this->productReviewRepository->findOrFail($id);

        $productReview->fill($request->input());

        $this->productReviewRepository->createOrUpdate($productReview);

        event(new UpdatedContentEvent(PRODUCT_REVIEW_MODULE_SCREEN_NAME, $request, $productReview));

        return $response
            ->setPreviousUrl(route('product-reviews.index'))
            ->withUpdatedSuccessMessage();
    }

    public function destroy(int $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $productReview = $this->productReviewRepository->findOrFail($id);

            $this->productReviewRepository->delete($productReview);

            event(new DeletedContentEvent(PRODUCT_REVIEW_MODULE_SCREEN_NAME, $request, $productReview));

            return $response->withDeletedSuccessMessage();
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function approve(int $id, BaseHttpResponse $response)
    {
        $productReview = $this->productReviewRepository->findOrFail($id);
        
        $productReview->update([
            'status' => ReviewStatusEnum::APPROVED,
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        return $response->withUpdatedSuccessMessage();
    }

    public function reject(int $id, BaseHttpResponse $response)
    {
        $productReview = $this->productReviewRepository->findOrFail($id);
        
        $productReview->update([
            'status' => ReviewStatusEnum::REJECTED,
        ]);

        return $response->withUpdatedSuccessMessage();
    }
}
```

## 7. Routes and Permissions

### Task 5.9: Configure Routes

1. Edit `platform/plugins/product-reviews/routes/web.php`:

```php
<?php

use Botble\Base\Facades\BaseHelper;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Botble\ProductReviews\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {
        Route::group(['prefix' => 'product-reviews', 'as' => 'product-reviews.'], function () {
            Route::resource('', 'ProductReviewController')->parameters(['' => 'product-review']);

            Route::post('{id}/approve', [
                'as' => 'approve',
                'uses' => 'ProductReviewController@approve',
                'permission' => 'product-reviews.edit',
            ]);

            Route::post('{id}/reject', [
                'as' => 'reject',
                'uses' => 'ProductReviewController@reject',
                'permission' => 'product-reviews.edit',
            ]);
        });
    });
});
```

### Task 5.10: Configure Permissions

1. Edit `platform/plugins/product-reviews/config/permissions.php`:

```php
<?php

return [
    [
        'name' => 'Product Reviews',
        'flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'product-reviews.create',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'product-reviews.edit',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'product-reviews.destroy',
        'parent_flag' => 'product-reviews.index',
    ],
];
```

## 8. Plugin Lifecycle

### Task 5.11: Configure Plugin Lifecycle

1. Edit `platform/plugins/product-reviews/src/Plugin.php`:

```php
<?php

namespace Botble\ProductReviews;

use Botble\PluginManagement\Abstracts\PluginOperationAbstract;
use Botble\Setting\Facades\Setting;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        Setting::set([
            'product_reviews_auto_approve' => false,
            'product_reviews_require_login' => true,
            'product_reviews_per_page' => 10,
        ])->save();
    }

    public static function deactivate(): void
    {
        // Plugin deactivation logic
    }

    public static function remove(): void
    {
        Schema::dropIfExists('product_reviews');

        Setting::delete([
            'product_reviews_auto_approve',
            'product_reviews_require_login',
            'product_reviews_per_page',
        ]);
    }
}
```

## 9. Testing the Plugin

### Task 5.12: Activate and Test

1. Activate the plugin:

```bash
php artisan cms:plugin:activate product-reviews
```

2. Run migrations:

```bash
php artisan migrate
```

3. Publish assets:

```bash
php artisan cms:plugin:assets:publish product-reviews
```

4. Clear cache:

```bash
php artisan cache:clear
```

## 10. Practice Assignment

### Assignment 5.1: Complete the Plugin

1. Create the form class for admin interface
2. Create the table class for listing reviews
3. Create request validation class
4. Create admin views
5. Test the complete functionality

### Assignment 5.2: Frontend Integration

1. Create frontend controller for customer reviews
2. Add review submission form
3. Display reviews on product pages
4. Add AJAX functionality for review submission

### Assignment 5.3: Advanced Features

1. Add email notifications for new reviews
2. Implement review moderation workflow
3. Add review statistics dashboard widget
4. Create review export functionality

## 11. Key Takeaways

1. **Plugin Structure**: Follow Botble CMS conventions for consistency
2. **Database Design**: Use proper relationships and constraints
3. **Repository Pattern**: Implement repository pattern for data access
4. **Service Provider**: Register all plugin components properly
5. **Admin Interface**: Use Botble's form and table builders
6. **Integration**: Use hooks and events for seamless integration
7. **Security**: Implement proper permissions and validation

## 9. Next Steps

In Lesson 6, we'll explore admin panel customization:
- Customizing dashboard widgets
- Adding custom admin sections
- Modifying admin interface
- Creating custom admin tools

## Quick Reference

### Plugin Commands
```bash
# Create plugin
php artisan cms:plugin:create <name>

# Activate plugin
php artisan cms:plugin:activate <name>

# Publish assets
php artisan cms:plugin:assets:publish <name>
```

### Key Files
- `plugin.json`: Plugin metadata
- `src/Providers/*ServiceProvider.php`: Main service provider
- `src/Models/*.php`: Eloquent models
- `database/migrations/*.php`: Database migrations
- `config/permissions.php`: Permission definitions
