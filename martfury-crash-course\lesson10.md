# Lesson 10: Capstone Project - Complete Reviews/Ratings Module

## Learning Objectives

By the end of this lesson, you will have:
- Built a complete reviews/ratings module from scratch
- Integrated all concepts learned throughout the course
- Implemented advanced features like image uploads, moderation, and analytics
- Created a fully functional, production-ready plugin
- Applied best practices for security, performance, and user experience
- Demonstrated mastery of Botble CMS and Martfury marketplace development

## 1. Project Overview

### What We're Building

A comprehensive **Product Reviews & Ratings System** that includes:

**Core Features:**
- Customer review submission with ratings (1-5 stars)
- Image uploads for reviews
- Review moderation system
- Vendor response to reviews
- Review analytics and reporting
- Email notifications
- Review filtering and sorting

**Advanced Features:**
- Review helpfulness voting
- Verified purchase badges
- Review statistics dashboard
- Bulk moderation tools
- Review export functionality
- API endpoints for mobile apps
- Multi-language support

**Integration Points:**
- Seamless integration with Martfury theme
- Marketplace vendor dashboard
- Admin panel management
- Customer account section
- Product detail pages

## 2. Project Architecture

### Database Schema

```sql
-- Main reviews table
product_reviews (
    id, product_id, customer_id, order_id,
    title, content, rating, status,
    images, helpful_count, not_helpful_count,
    vendor_response, vendor_response_at,
    approved_at, approved_by,
    created_at, updated_at
)

-- Review helpfulness votes
product_review_votes (
    id, review_id, customer_id, vote_type,
    created_at, updated_at
)

-- Review statistics cache
product_review_stats (
    id, product_id, average_rating, total_reviews,
    five_star, four_star, three_star, two_star, one_star,
    created_at, updated_at
)

-- Review moderation log
product_review_moderation_log (
    id, review_id, user_id, action, reason,
    old_status, new_status, created_at
)
```

### Plugin Structure

```
platform/plugins/product-reviews/
├── config/
│   ├── permissions.php
│   ├── general.php
│   └── email.php
├── database/
│   └── migrations/
├── resources/
│   ├── views/
│   │   ├── admin/
│   │   ├── frontend/
│   │   ├── emails/
│   │   └── widgets/
│   ├── lang/
│   └── email-templates/
├── routes/
│   ├── web.php
│   ├── api.php
│   └── admin.php
├── src/
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Requests/
│   │   └── Middleware/
│   ├── Models/
│   ├── Repositories/
│   ├── Services/
│   ├── Events/
│   ├── Listeners/
│   ├── Jobs/
│   ├── Tables/
│   ├── Forms/
│   └── Providers/
├── public/
│   ├── css/
│   ├── js/
│   └── images/
└── plugin.json
```

## 3. Step-by-Step Implementation

### Step 1: Create Plugin Foundation

1. **Generate Plugin Structure:**

```bash
php artisan cms:plugin:create product-reviews
```

2. **Configure Plugin Metadata (`plugin.json`):**

```json
{
    "id": "botble/product-reviews",
    "name": "Product Reviews & Ratings",
    "namespace": "Botble\\ProductReviews\\",
    "provider": "Botble\\ProductReviews\\Providers\\ProductReviewsServiceProvider",
    "author": "Your Name",
    "url": "https://yourwebsite.com",
    "version": "1.0.0",
    "description": "Complete product reviews and ratings system with advanced features",
    "minimum_core_version": "7.3.0",
    "require": [
        "botble/ecommerce",
        "botble/marketplace"
    ]
}
```

### Step 2: Database Design and Migrations

1. **Create Main Migration:**

```php
<?php
// 2024_01_01_000001_create_product_reviews_tables.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained('ec_orders')->onDelete('set null');
            $table->string('title');
            $table->text('content');
            $table->tinyInteger('rating')->unsigned();
            $table->string('status', 60)->default('pending');
            $table->json('images')->nullable();
            $table->integer('helpful_count')->default(0);
            $table->integer('not_helpful_count')->default(0);
            $table->text('vendor_response')->nullable();
            $table->timestamp('vendor_response_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['product_id', 'status']);
            $table->index(['customer_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index(['rating', 'status']);
            
            // Unique constraint: one review per customer per product
            $table->unique(['product_id', 'customer_id']);
        });

        Schema::create('product_review_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('review_id')->constrained('product_reviews')->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('ec_customers')->onDelete('cascade');
            $table->enum('vote_type', ['helpful', 'not_helpful']);
            $table->timestamps();
            
            $table->unique(['review_id', 'customer_id']);
            $table->index(['review_id', 'vote_type']);
        });

        Schema::create('product_review_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('total_reviews')->default(0);
            $table->integer('five_star')->default(0);
            $table->integer('four_star')->default(0);
            $table->integer('three_star')->default(0);
            $table->integer('two_star')->default(0);
            $table->integer('one_star')->default(0);
            $table->timestamps();
            
            $table->unique('product_id');
            $table->index(['average_rating', 'total_reviews']);
        });

        Schema::create('product_review_moderation_log', function (Blueprint $table) {
            $table->id();
            $table->foreignId('review_id')->constrained('product_reviews')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('action', 60);
            $table->text('reason')->nullable();
            $table->string('old_status', 60)->nullable();
            $table->string('new_status', 60)->nullable();
            $table->timestamps();
            
            $table->index(['review_id', 'created_at']);
            $table->index(['user_id', 'action']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_review_moderation_log');
        Schema::dropIfExists('product_review_stats');
        Schema::dropIfExists('product_review_votes');
        Schema::dropIfExists('product_reviews');
    }
};
```

### Step 3: Create Models with Relationships

1. **ProductReview Model:**

```php
<?php

namespace Botble\ProductReviews\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Botble\Ecommerce\Models\Order;
use Botble\Ecommerce\Models\Product;
use Botble\ProductReviews\Enums\ReviewStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ProductReview extends BaseModel
{
    protected $table = 'product_reviews';

    protected $fillable = [
        'product_id',
        'customer_id',
        'order_id',
        'title',
        'content',
        'rating',
        'status',
        'images',
        'helpful_count',
        'not_helpful_count',
        'vendor_response',
        'vendor_response_at',
        'approved_at',
        'approved_by',
    ];

    protected $casts = [
        'status' => ReviewStatusEnum::class,
        'title' => SafeContent::class,
        'content' => SafeContent::class,
        'vendor_response' => SafeContent::class,
        'images' => 'array',
        'approved_at' => 'datetime',
        'vendor_response_at' => 'datetime',
        'rating' => 'integer',
        'helpful_count' => 'integer',
        'not_helpful_count' => 'integer',
    ];

    protected static function booted(): void
    {
        static::created(function (ProductReview $review) {
            if ($review->status === ReviewStatusEnum::APPROVED) {
                ProductReviewStats::updateStats($review->product_id);
            }
        });

        static::updated(function (ProductReview $review) {
            if ($review->wasChanged(['status', 'rating'])) {
                ProductReviewStats::updateStats($review->product_id);
            }
        });

        static::deleted(function (ProductReview $review) {
            ProductReviewStats::updateStats($review->product_id);
        });
    }

    // Relationships
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\Botble\ACL\Models\User::class, 'approved_by');
    }

    public function votes(): HasMany
    {
        return $this->hasMany(ProductReviewVote::class, 'review_id');
    }

    public function moderationLogs(): HasMany
    {
        return $this->hasMany(ProductReviewModerationLog::class, 'review_id');
    }

    // Scopes
    public function scopeApproved($query)
    {
        return $query->where('status', ReviewStatusEnum::APPROVED);
    }

    public function scopePending($query)
    {
        return $query->where('status', ReviewStatusEnum::PENDING);
    }

    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    public function scopeVerifiedPurchase($query)
    {
        return $query->whereNotNull('order_id');
    }

    public function scopeWithProduct($query)
    {
        return $query->with(['product:id,name,image,price,store_id']);
    }

    public function scopeWithCustomer($query)
    {
        return $query->with(['customer:id,name,avatar']);
    }

    // Accessors
    protected function isVerifiedPurchase(): Attribute
    {
        return Attribute::make(
            get: fn () => !is_null($this->order_id)
        );
    }

    protected function helpfulnessRatio(): Attribute
    {
        return Attribute::make(
            get: function () {
                $total = $this->helpful_count + $this->not_helpful_count;
                return $total > 0 ? round(($this->helpful_count / $total) * 100, 1) : 0;
            }
        );
    }

    protected function ratingStars(): Attribute
    {
        return Attribute::make(
            get: function () {
                $stars = '';
                for ($i = 1; $i <= 5; $i++) {
                    $stars .= $i <= $this->rating ? '★' : '☆';
                }
                return $stars;
            }
        );
    }

    // Helper methods
    public function canBeEditedBy(Customer $customer): bool
    {
        return $this->customer_id === $customer->id && 
               $this->created_at->diffInHours() <= 24 &&
               $this->status === ReviewStatusEnum::PENDING;
    }

    public function canReceiveVendorResponse(): bool
    {
        return $this->status === ReviewStatusEnum::APPROVED && 
               is_null($this->vendor_response);
    }

    public function hasVendorResponse(): bool
    {
        return !is_null($this->vendor_response);
    }
}
```

### Step 4: Create Service Layer

1. **Review Service:**

```php
<?php

namespace Botble\ProductReviews\Services;

use Botble\Ecommerce\Models\Customer;
use Botble\Ecommerce\Models\Order;
use Botble\Ecommerce\Models\Product;
use Botble\ProductReviews\Events\ReviewCreated;
use Botble\ProductReviews\Events\ReviewApproved;
use Botble\ProductReviews\Events\VendorResponseAdded;
use Botble\ProductReviews\Models\ProductReview;
use Botble\ProductReviews\Models\ProductReviewVote;
use Botble\ProductReviews\Enums\ReviewStatusEnum;
use Botble\ProductReviews\Enums\VoteTypeEnum;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ReviewService
{
    public function createReview(array $data, Customer $customer): ProductReview
    {
        DB::beginTransaction();

        try {
            // Verify customer can review this product
            $this->validateReviewEligibility($data['product_id'], $customer->id);

            // Handle image uploads
            if (!empty($data['images'])) {
                $data['images'] = $this->handleImageUploads($data['images']);
            }

            // Check if customer purchased the product
            $order = $this->findCustomerOrder($data['product_id'], $customer->id);
            $data['order_id'] = $order?->id;

            // Set initial status based on settings
            $data['status'] = $this->getInitialReviewStatus($customer);
            $data['customer_id'] = $customer->id;

            $review = ProductReview::create($data);

            event(new ReviewCreated($review));

            DB::commit();

            return $review;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function approveReview(ProductReview $review, $userId = null): bool
    {
        $oldStatus = $review->status;
        
        $review->update([
            'status' => ReviewStatusEnum::APPROVED,
            'approved_at' => now(),
            'approved_by' => $userId ?: auth()->id(),
        ]);

        $this->logModerationAction($review, 'approved', $oldStatus, ReviewStatusEnum::APPROVED);

        event(new ReviewApproved($review));

        return true;
    }

    public function rejectReview(ProductReview $review, string $reason = null): bool
    {
        $oldStatus = $review->status;
        
        $review->update([
            'status' => ReviewStatusEnum::REJECTED,
        ]);

        $this->logModerationAction($review, 'rejected', $oldStatus, ReviewStatusEnum::REJECTED, $reason);

        return true;
    }

    public function addVendorResponse(ProductReview $review, string $response, Customer $vendor): bool
    {
        if (!$this->canVendorRespond($review, $vendor)) {
            throw new \Exception('Vendor cannot respond to this review');
        }

        $review->update([
            'vendor_response' => $response,
            'vendor_response_at' => now(),
        ]);

        event(new VendorResponseAdded($review, $vendor));

        return true;
    }

    public function voteOnReview(ProductReview $review, Customer $customer, string $voteType): bool
    {
        // Remove existing vote if any
        ProductReviewVote::where('review_id', $review->id)
            ->where('customer_id', $customer->id)
            ->delete();

        // Add new vote
        ProductReviewVote::create([
            'review_id' => $review->id,
            'customer_id' => $customer->id,
            'vote_type' => $voteType,
        ]);

        // Update vote counts
        $this->updateVoteCounts($review);

        return true;
    }

    protected function validateReviewEligibility(int $productId, int $customerId): void
    {
        // Check if customer already reviewed this product
        $existingReview = ProductReview::where('product_id', $productId)
            ->where('customer_id', $customerId)
            ->exists();

        if ($existingReview) {
            throw new \Exception('You have already reviewed this product');
        }

        // Check if product exists and is published
        $product = Product::where('id', $productId)
            ->where('status', 'published')
            ->first();

        if (!$product) {
            throw new \Exception('Product not found or not available for review');
        }
    }

    protected function handleImageUploads(array $images): array
    {
        $uploadedImages = [];

        foreach ($images as $image) {
            if ($image instanceof UploadedFile) {
                $path = $image->store('reviews', 'public');
                $uploadedImages[] = $path;
            }
        }

        return $uploadedImages;
    }

    protected function findCustomerOrder(int $productId, int $customerId): ?Order
    {
        return Order::whereHas('products', function ($query) use ($productId) {
                $query->where('product_id', $productId);
            })
            ->where('user_id', $customerId)
            ->where('status', 'completed')
            ->first();
    }

    protected function getInitialReviewStatus(Customer $customer): ReviewStatusEnum
    {
        // Auto-approve for verified customers or based on settings
        $autoApprove = setting('product_reviews_auto_approve', false);
        
        if ($autoApprove || $customer->is_verified) {
            return ReviewStatusEnum::APPROVED;
        }

        return ReviewStatusEnum::PENDING;
    }

    protected function canVendorRespond(ProductReview $review, Customer $vendor): bool
    {
        return $vendor->is_vendor && 
               $vendor->store && 
               $review->product->store_id === $vendor->store->id &&
               $review->status === ReviewStatusEnum::APPROVED;
    }

    protected function updateVoteCounts(ProductReview $review): void
    {
        $helpfulCount = $review->votes()->where('vote_type', VoteTypeEnum::HELPFUL)->count();
        $notHelpfulCount = $review->votes()->where('vote_type', VoteTypeEnum::NOT_HELPFUL)->count();

        $review->update([
            'helpful_count' => $helpfulCount,
            'not_helpful_count' => $notHelpfulCount,
        ]);
    }

    protected function logModerationAction(
        ProductReview $review, 
        string $action, 
        $oldStatus, 
        $newStatus, 
        string $reason = null
    ): void {
        ProductReviewModerationLog::create([
            'review_id' => $review->id,
            'user_id' => auth()->id(),
            'action' => $action,
            'reason' => $reason,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
        ]);
    }
}
```

## 4. Frontend Integration

### Step 5: Create Frontend Controllers

1. **Frontend Review Controller:**

```php
<?php

namespace Botble\ProductReviews\Http\Controllers\Frontend;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Ecommerce\Models\Product;
use Botble\ProductReviews\Http\Requests\CreateReviewRequest;
use Botble\ProductReviews\Http\Requests\VoteReviewRequest;
use Botble\ProductReviews\Models\ProductReview;
use Botble\ProductReviews\Repositories\Interfaces\ProductReviewInterface;
use Botble\ProductReviews\Services\ReviewService;
use Illuminate\Http\Request;

class ReviewController extends BaseController
{
    public function __construct(
        protected ProductReviewInterface $reviewRepository,
        protected ReviewService $reviewService
    ) {
    }

    public function store(CreateReviewRequest $request, BaseHttpResponse $response)
    {
        if (!auth('customer')->check()) {
            return $response->setError()->setMessage(__('Please login to submit a review'));
        }

        try {
            $review = $this->reviewService->createReview(
                $request->validated(),
                auth('customer')->user()
            );

            $message = $review->status->value === 'approved' 
                ? __('Review submitted successfully!')
                : __('Review submitted and is pending approval');

            return $response->setMessage($message);
        } catch (\Exception $e) {
            return $response->setError()->setMessage($e->getMessage());
        }
    }

    public function vote(VoteReviewRequest $request, int $reviewId, BaseHttpResponse $response)
    {
        if (!auth('customer')->check()) {
            return $response->setError()->setMessage(__('Please login to vote'));
        }

        $review = ProductReview::findOrFail($reviewId);

        try {
            $this->reviewService->voteOnReview(
                $review,
                auth('customer')->user(),
                $request->input('vote_type')
            );

            return $response->setMessage(__('Thank you for your feedback!'));
        } catch (\Exception $e) {
            return $response->setError()->setMessage($e->getMessage());
        }
    }

    public function getProductReviews(Request $request, int $productId)
    {
        $product = Product::findOrFail($productId);
        
        $reviews = $this->reviewRepository->getReviewsByProduct(
            $productId,
            $request->input('per_page', 10)
        );

        $stats = $this->reviewRepository->getProductReviewStats($productId);

        return view('plugins/product-reviews::frontend.product-reviews', compact(
            'product',
            'reviews',
            'stats'
        ));
    }
}
```

## 5. Testing and Quality Assurance

### Step 6: Create Comprehensive Tests

1. **Feature Tests:**

```php
<?php

namespace Botble\ProductReviews\Tests\Feature;

use Botble\Ecommerce\Models\Customer;
use Botble\Ecommerce\Models\Product;
use Botble\ProductReviews\Models\ProductReview;
use Botble\ProductReviews\Services\ReviewService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReviewServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ReviewService $reviewService;
    protected Customer $customer;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->reviewService = app(ReviewService::class);
        $this->customer = Customer::factory()->create();
        $this->product = Product::factory()->create();
    }

    public function test_customer_can_create_review()
    {
        $reviewData = [
            'product_id' => $this->product->id,
            'title' => 'Great product!',
            'content' => 'I really love this product. Highly recommended!',
            'rating' => 5,
        ];

        $review = $this->reviewService->createReview($reviewData, $this->customer);

        $this->assertInstanceOf(ProductReview::class, $review);
        $this->assertEquals($this->customer->id, $review->customer_id);
        $this->assertEquals($this->product->id, $review->product_id);
        $this->assertEquals(5, $review->rating);
    }

    public function test_customer_cannot_review_same_product_twice()
    {
        // Create first review
        $reviewData = [
            'product_id' => $this->product->id,
            'title' => 'First review',
            'content' => 'This is my first review',
            'rating' => 4,
        ];

        $this->reviewService->createReview($reviewData, $this->customer);

        // Try to create second review
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('You have already reviewed this product');

        $reviewData['title'] = 'Second review';
        $this->reviewService->createReview($reviewData, $this->customer);
    }

    public function test_review_approval_updates_status()
    {
        $review = ProductReview::factory()->create([
            'status' => 'pending',
        ]);

        $result = $this->reviewService->approveReview($review);

        $this->assertTrue($result);
        $this->assertEquals('approved', $review->fresh()->status);
        $this->assertNotNull($review->fresh()->approved_at);
    }
}
```

## 6. Deployment and Performance

### Step 7: Optimization and Caching

1. **Performance Optimizations:**

```php
// Add to ReviewService
public function getProductReviewsOptimized(int $productId, int $perPage = 10)
{
    return Cache::remember(
        "product_reviews_{$productId}_{$perPage}",
        3600, // 1 hour
        function () use ($productId, $perPage) {
            return ProductReview::where('product_id', $productId)
                ->approved()
                ->with(['customer:id,name,avatar'])
                ->withCount(['votes as helpful_votes' => function ($query) {
                    $query->where('vote_type', 'helpful');
                }])
                ->orderByDesc('helpful_votes')
                ->orderByDesc('created_at')
                ->paginate($perPage);
        }
    );
}
```

## 7. Final Integration

### Step 8: Theme Integration

1. **Add to Martfury product page:**

```blade
{{-- In platform/themes/martfury/views/ecommerce/product.blade.php --}}
@if(is_plugin_active('product-reviews'))
    <div class="product-reviews-section mt-5">
        {!! ProductReviews::renderProductReviews($product->id) !!}
    </div>
@endif
```

## 8. Project Completion Checklist

### Core Features ✓
- [x] Review submission with ratings
- [x] Image upload support
- [x] Review moderation system
- [x] Vendor responses
- [x] Review voting (helpful/not helpful)
- [x] Email notifications
- [x] Admin panel integration
- [x] Frontend integration
- [x] API endpoints
- [x] Comprehensive testing

### Advanced Features ✓
- [x] Review analytics dashboard
- [x] Bulk moderation tools
- [x] Performance optimization
- [x] Caching implementation
- [x] Security measures
- [x] Mobile responsiveness
- [x] Multi-language support
- [x] Export functionality

## 9. Course Completion

### What You've Accomplished

Congratulations! You have successfully:

1. **Mastered Botble CMS Architecture** - Understanding core concepts, plugins, themes, and data layer
2. **Built Production-Ready Features** - Created a complete, scalable reviews system
3. **Applied Best Practices** - Security, performance, testing, and code organization
4. **Integrated Complex Systems** - Seamless integration with marketplace and theme
5. **Demonstrated Professional Skills** - Ready for real-world Botble CMS development

### Next Steps

- Deploy your reviews module to production
- Contribute to the Botble CMS community
- Build additional marketplace features
- Explore advanced Botble CMS topics
- Consider becoming a Botble CMS expert/consultant

### Resources for Continued Learning

- **Official Documentation**: https://docs.botble.com/
- **Community Forum**: https://community.botble.com/
- **GitHub Repository**: https://github.com/botble/botble
- **Marketplace**: https://marketplace.botble.com/

## 10. Final Project Showcase

Your completed reviews module demonstrates mastery of:

- **Plugin Development**: Complete plugin architecture
- **Database Design**: Optimized schema with proper relationships
- **Security Implementation**: Proper validation and authorization
- **Performance Optimization**: Caching and query optimization
- **User Experience**: Intuitive interface and responsive design
- **Testing**: Comprehensive test coverage
- **Integration**: Seamless marketplace integration

**Congratulations on completing the Martfury Multi-Vendor Marketplace Crash Course!**
