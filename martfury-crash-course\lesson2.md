# Lesson 2: Theme Customization Essentials

## Learning Objectives

By the end of this lesson, you will understand:
- Blade template system and template hierarchy
- Layout structure and customization
- Partials system and reusable components
- Theme options and configuration
- Navigation menu system
- Asset management and compilation
- Widget system and sidebar management
- Responsive design implementation

## 1. Blade Template System in Martfury

### Template Hierarchy

Botble CMS uses a hierarchical template system where templates are resolved in the following order:

1. **Theme Views**: `platform/themes/martfury/views/`
2. **Plugin Views**: `platform/plugins/{plugin}/resources/views/`
3. **Core Views**: `platform/core/{module}/resources/views/`

### Key Template Files

```
platform/themes/martfury/
├── layouts/                    # Page layouts
│   ├── default.blade.php      # Default page layout
│   ├── homepage.blade.php     # Homepage layout
│   ├── blog-sidebar.blade.php # Blog with sidebar
│   ├── full-width.blade.php   # Full width layout
│   └── coming-soon.blade.php  # Coming soon page
├── partials/                   # Reusable components
│   ├── header.blade.php       # Site header
│   ├── footer.blade.php       # Site footer
│   ├── menu.blade.php         # Navigation menu
│   └── breadcrumbs.blade.php  # Breadcrumb navigation
└── views/                      # Page templates
    ├── index.blade.php        # Homepage template
    ├── page.blade.php         # Static page template
    ├── post.blade.php         # Blog post template
    └── ecommerce/             # E-commerce templates
```

## 2. Understanding Layouts

### Task 2.1: Examine the Default Layout

1. Open `platform/themes/martfury/layouts/default.blade.php`:

```blade
{!! Theme::partial('header') !!}
<div class="ps-breadcrumb">
    <div class="ps-container">
        {!! Theme::partial('breadcrumbs') !!}
    </div>
</div>

<div class="ps-container">
    <div class="mt-40 mb-40">
        {!! Theme::content() !!}
    </div>
</div>

{!! Theme::partial('footer') !!}
```

**Understanding**: This layout includes header, breadcrumbs, main content area, and footer.

### Layout Components Explained

- `Theme::partial('header')`: Includes the header partial
- `Theme::content()`: Renders the main page content
- `Theme::partial('footer')`: Includes the footer partial

### Task 2.2: Create a Custom Layout

1. Create a new layout file: `platform/themes/martfury/layouts/custom-sidebar.blade.php`

```blade
{!! Theme::partial('header') !!}
<div class="ps-breadcrumb">
    <div class="ps-container">
        {!! Theme::partial('breadcrumbs') !!}
    </div>
</div>

<div class="ps-container">
    <div class="row mt-40 mb-40">
        <div class="col-lg-9">
            {!! Theme::content() !!}
        </div>
        <div class="col-lg-3">
            <div class="ps-sidebar">
                {!! dynamic_sidebar('primary_sidebar') !!}
            </div>
        </div>
    </div>
</div>

{!! Theme::partial('footer') !!}
```

2. Register the layout in `platform/themes/martfury/functions/functions.php`:

```php
register_page_template([
    'blog-sidebar' => __('Blog Sidebar'),
    'full-width' => __('Full width'),
    'homepage' => __('Homepage'),
    'coming-soon' => __('Coming soon'),
    'custom-sidebar' => __('Custom Sidebar Layout'), // Add this line
]);
```

## 3. Working with Partials

### Understanding Partials

Partials are reusable template components that can be included in layouts and views. They promote code reusability and maintainability.

### Task 2.3: Examine the Header Partial

1. Open `platform/themes/martfury/partials/header.blade.php` (first 50 lines):

```blade
{!! Theme::partial('header-meta') !!}
<body @if (Theme::get('pageId')) id="{{ Theme::get('pageId') }}" @endif>
    {!! apply_filters(THEME_FRONT_BODY, null) !!}
    <div id="alert-container"></div>

    @if (theme_option('preloader_enabled', 'no') == 'yes')
        <div id="loader-wrapper">
            <div class="preloader-loading"></div>
        </div>
    @endif

    <header class="header header--1">
        <div class="header__top">
            <div class="ps-container align-items-center">
                @if (is_plugin_active('ecommerce'))
                    <div class="header__left">
                        <div class="menu--product-categories">
                            <div class="menu__toggle">
                                <i class="icon-menu"></i>
                                <span>{{ __('Shop by Department') }}</span>
                            </div>
                        </div>
                        <a class="ps-logo" href="{{ BaseHelper::getHomepageUrl() }}">
                            {!! Theme::getLogoImage(['style' => 'max-height: 40px']) !!}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </header>
```

### Task 2.4: Create a Custom Partial

1. Create `platform/themes/martfury/partials/custom-banner.blade.php`:

```blade
@if (theme_option('show_custom_banner', 'yes') == 'yes')
<div class="custom-banner">
    <div class="ps-container">
        <div class="banner-content">
            <h2>{{ theme_option('banner_title', 'Welcome to Our Store') }}</h2>
            <p>{{ theme_option('banner_description', 'Find amazing products at great prices') }}</p>
            @if (theme_option('banner_button_text'))
                <a href="{{ theme_option('banner_button_url', '#') }}" class="ps-btn">
                    {{ theme_option('banner_button_text') }}
                </a>
            @endif
        </div>
    </div>
</div>
@endif
```

2. Include it in a layout:

```blade
{!! Theme::partial('header') !!}
{!! Theme::partial('custom-banner') !!}
<!-- Rest of layout -->
```

## 4. Theme Options System

### Understanding Theme Options

Theme options allow users to customize theme settings through the admin panel without editing code.

### Task 2.5: Examine Existing Theme Options

1. Open `platform/themes/martfury/functions/theme-options.php`:

```php
app()->booted(function (): void {
    theme_option()
        ->setSection([
            'title' => __('Style'),
            'desc' => __('Style of theme'),
            'id' => 'opt-text-subsection-style',
            'subsection' => true,
            'icon' => 'ti ti-brush',
        ])
        ->setField([
            'id' => 'primary_font',
            'section_id' => 'opt-text-subsection-style',
            'type' => 'googleFonts',
            'label' => __('Primary font'),
            'attributes' => [
                'name' => 'primary_font',
                'value' => 'Work Sans',
            ],
        ])
        ->setField([
            'id' => 'primary_color',
            'section_id' => 'opt-text-subsection-style',
            'type' => 'customColor',
            'label' => __('Primary color'),
            'attributes' => [
                'name' => 'primary_color',
                'value' => '#fcb800',
            ],
        ]);
});
```

### Task 2.6: Add Custom Theme Options

1. Add to `platform/themes/martfury/functions/theme-options.php`:

```php
// Add after existing theme options
->setSection([
    'title' => __('Custom Banner'),
    'desc' => __('Configure custom banner settings'),
    'id' => 'opt-text-subsection-banner',
    'subsection' => true,
    'icon' => 'ti ti-photo',
])
->setField([
    'id' => 'show_custom_banner',
    'section_id' => 'opt-text-subsection-banner',
    'type' => 'onOff',
    'label' => __('Show Custom Banner'),
    'attributes' => [
        'name' => 'show_custom_banner',
        'value' => 'yes',
    ],
])
->setField([
    'id' => 'banner_title',
    'section_id' => 'opt-text-subsection-banner',
    'type' => 'text',
    'label' => __('Banner Title'),
    'attributes' => [
        'name' => 'banner_title',
        'value' => 'Welcome to Our Store',
    ],
])
->setField([
    'id' => 'banner_description',
    'section_id' => 'opt-text-subsection-banner',
    'type' => 'textarea',
    'label' => __('Banner Description'),
    'attributes' => [
        'name' => 'banner_description',
        'value' => 'Find amazing products at great prices',
    ],
]);
```

### Using Theme Options in Templates

```blade
<!-- Get theme option value -->
{{ theme_option('primary_color', '#fcb800') }}

<!-- Check if option is enabled -->
@if (theme_option('show_custom_banner', 'no') == 'yes')
    <!-- Banner content -->
@endif
```

## 5. Navigation Menu System

### Task 2.7: Examine Menu Implementation

1. Look at `platform/themes/martfury/partials/menu.blade.php`:

```blade
@if ($menu)
    <ul {!! $options !!}>
        @foreach ($menu as $item)
            <li @if ($item->css_class) class="{{ $item->css_class }}" @endif>
                <a href="{{ $item->url }}" target="{{ $item->target }}">
                    @if ($item->icon_font)
                        <i class="{{ $item->icon_font }}"></i>
                    @endif
                    {{ $item->title }}
                </a>
                @if ($item->has_child)
                    {!! Menu::generateMenu([
                        'menu' => $item->child,
                        'view' => 'main-menu',
                    ]) !!}
                @endif
            </li>
        @endforeach
    </ul>
@endif
```

### Rendering Menus in Templates

```blade
<!-- Render main menu -->
{!! Menu::renderMenuLocation('main-menu', [
    'view' => 'main-menu',
    'options' => ['class' => 'menu'],
]) !!}

<!-- Render footer menu -->
{!! Menu::renderMenuLocation('footer-menu', [
    'view' => 'footer-menu',
]) !!}
```

## 6. Asset Management and Compilation

### Understanding Asset Workflow

1. **Source Files**: `platform/themes/martfury/assets/`
2. **Compilation**: Laravel Mix processes SASS and JS
3. **Output**: Compiled files go to `public/themes/martfury/`
4. **Theme Copy**: Also copied to `platform/themes/martfury/public/`

### Task 2.8: Examine Asset Compilation

1. Look at `platform/themes/martfury/webpack.mix.js`:

```javascript
let mix = require('laravel-mix')
const path = require('path')
let directory = path.basename(path.resolve(__dirname))

const source = 'platform/themes/' + directory
const dist = 'public/themes/' + directory

mix
    .sass(source + '/assets/sass/style.scss', dist + '/css')
    .sass(source + '/assets/sass/rtl.scss', dist + '/css')
    .js(source + '/assets/js/main.js', dist + '/js')
    .js(source + '/assets/js/backend.js', dist + '/js')
    .copy(dist + '/css/style.css', source + '/public/css')
    .copy(dist + '/css/rtl.css', source + '/public/css')
    .copy(dist + '/js/main.js', source + '/public/js')
    .copy(dist + '/js/backend.js', source + '/public/js')
```

### Task 2.9: Add Custom Assets

1. Create `platform/themes/martfury/assets/sass/custom.scss`:

```scss
// Custom styles
.custom-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    
    h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
    
    .ps-btn {
        background: white;
        color: #667eea;
        padding: 12px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: bold;
        
        &:hover {
            background: #f8f9fa;
        }
    }
}
```

2. Update `webpack.mix.js`:

```javascript
mix
    .sass(source + '/assets/sass/style.scss', dist + '/css')
    .sass(source + '/assets/sass/rtl.scss', dist + '/css')
    .sass(source + '/assets/sass/custom.scss', dist + '/css') // Add this line
    // ... rest of configuration
```

3. Register the asset in `platform/themes/martfury/config.php`:

```php
'beforeRenderTheme' => function($theme) {
    // Existing assets...
    $theme->asset()->usePath()->add('custom-style', 'css/custom.css');
}
```

## 7. Widget System

### Understanding Widgets

Widgets are reusable components that can be placed in sidebars and widget areas.

### Task 2.10: Examine Widget Structure

1. Look at `platform/themes/martfury/widgets/custom-menu/custom-menu.php`:

```php
<?php

use Botble\Widget\AbstractWidget;

class CustomMenuWidget extends AbstractWidget
{
    public function __construct()
    {
        parent::__construct([
            'name' => __('Custom Menu'),
            'description' => __('Display custom menu widget'),
            'menu_id' => null,
        ]);
    }
}
```

### Registering Widget Areas

In `platform/themes/martfury/functions/functions.php`:

```php
register_sidebar([
    'id' => 'footer_sidebar',
    'name' => __('Footer sidebar'),
    'description' => __('Widgets in footer of page'),
]);

register_sidebar([
    'id' => 'bottom_footer_sidebar',
    'name' => __('Bottom Footer sidebar'),
    'description' => __('Widgets in bottom footer'),
]);
```

### Displaying Widgets in Templates

```blade
<!-- Display widgets in sidebar -->
{!! dynamic_sidebar('footer_sidebar') !!}

<!-- Display specific widget area -->
<div class="widget-area">
    {!! dynamic_sidebar('primary_sidebar') !!}
</div>
```

## 8. Responsive Design Implementation

### Task 2.11: Examine Responsive Structure

1. Look at responsive classes in templates:

```blade
<div class="row">
    <div class="col-lg-9 col-md-8">
        <!-- Main content -->
    </div>
    <div class="col-lg-3 col-md-4">
        <!-- Sidebar -->
    </div>
</div>
```

### Mobile-Specific Partials

Martfury includes mobile-specific partials:
- `header-mobile.blade.php`
- `header-mobile-product.blade.php`

## 9. Practice Assignment

### Assignment 2.1: Create a Custom Homepage Section

1. Create a new partial: `platform/themes/martfury/partials/featured-section.blade.php`
2. Add theme options for the section
3. Include it in the homepage layout
4. Style it with custom CSS

### Assignment 2.2: Customize the Header

1. Modify the header partial to include a promotional banner
2. Add theme options to control the banner
3. Make it responsive

## 10. Key Takeaways

1. **Template Hierarchy**: Understanding how templates are resolved
2. **Layouts vs Partials**: Layouts define structure, partials are reusable components
3. **Theme Options**: Allow customization without code changes
4. **Asset Compilation**: SASS and JS are compiled using Laravel Mix
5. **Widget System**: Provides flexible content areas
6. **Responsive Design**: Mobile-first approach with Bootstrap grid

## 11. Next Steps

In Lesson 3, we'll explore the plugin system in depth, focusing on:
- Plugin architecture and structure
- Marketplace-specific functionality
- How plugins integrate with themes
- Creating custom plugin features

## Quick Reference

### Important Functions
```php
// Theme functions
Theme::partial('partial-name')
Theme::content()
theme_option('option_name', 'default_value')

// Menu functions
Menu::renderMenuLocation('location-name')

// Widget functions
dynamic_sidebar('sidebar_id')
register_sidebar([...])
```

### File Paths
- Layouts: `platform/themes/martfury/layouts/`
- Partials: `platform/themes/martfury/partials/`
- Assets: `platform/themes/martfury/assets/`
- Theme Options: `platform/themes/martfury/functions/theme-options.php`
