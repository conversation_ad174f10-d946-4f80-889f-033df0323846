# Lesson 8: Data Layer Management

## Learning Objectives

By the end of this lesson, you will understand:
- Eloquent models and relationships in Botble CMS
- Repository pattern implementation
- Database migrations and schema design
- Model traits and shared functionality
- Query optimization and performance
- Database relationships in marketplace context
- Model scopes and query builders
- Data validation and model events
- Caching strategies for data layer

## 1. BaseModel Architecture

### Understanding BaseModel

All models in Botble CMS extend the `BaseModel` class, which provides:
- UUID or integer ID support
- Metadata functionality
- Enhanced query builder
- Consistent model behavior

### Task 8.1: Examine BaseModel

1. Look at `platform/core/base/src/Models/BaseModel.php`:

```php
class BaseModel extends Model implements BaseModelContract
{
    use HasBaseEloquentBuilder;
    use HasMetadata;
    use HasUuidsOrIntegerIds;

    public function __get($key)
    {
        if (MacroableModels::modelHasMacro(static::class, $method = 'get' . Str::studly($key) . 'Attribute')) {
            return $this->{$method}();
        }

        return parent::__get($key);
    }
}
```

### BaseModel Features

- **HasMetadata**: Allows storing additional data without schema changes
- **HasUuidsOrIntegerIds**: Flexible ID type support
- **HasBaseEloquentBuilder**: Enhanced query capabilities
- **MacroableModels**: Dynamic attribute support

## 2. Model Structure and Relationships

### Task 8.2: Examine Product Model

1. Look at `platform/plugins/ecommerce/src/Models/Product.php`:

```php
class Product extends BaseModel
{
    use Concerns\ProductPrices;

    protected $table = 'ec_products';

    protected $fillable = [
        'name',
        'description',
        'content',
        'image',
        'images',
        'sku',
        'price',
        'sale_price',
        'quantity',
        'brand_id',
        'is_featured',
        'status',
        // ... more fields
    ];

    protected $appends = [
        'original_price',
        'front_sale_price',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'images' => 'array',
        'is_featured' => 'boolean',
        'price' => 'float',
        'sale_price' => 'float',
    ];
}
```

### Key Model Features

- **Fillable**: Mass assignment protection
- **Casts**: Automatic type conversion
- **Appends**: Virtual attributes
- **Traits**: Shared functionality

### Task 8.3: Examine Store Model Relationships

1. Look at `platform/plugins/marketplace/src/Models/Store.php`:

```php
class Store extends BaseModel
{
    use LocationTrait;

    protected $table = 'mp_stores';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'customer_id',
        'logo',
        'description',
        'status',
        // ... more fields
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'store_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'store_id');
    }

    public function revenues(): HasMany
    {
        return $this->hasMany(Revenue::class, 'store_id');
    }
}
```

## 3. Database Relationships

### Common Relationship Types

#### One-to-Many (HasMany/BelongsTo)

```php
// Store has many products
public function products(): HasMany
{
    return $this->hasMany(Product::class, 'store_id');
}

// Product belongs to store
public function store(): BelongsTo
{
    return $this->belongsTo(Store::class, 'store_id');
}
```

#### Many-to-Many (BelongsToMany)

```php
// Product belongs to many categories
public function categories(): BelongsToMany
{
    return $this->belongsToMany(
        ProductCategory::class,
        'ec_product_categories',
        'product_id',
        'category_id'
    );
}

// Flash sale with pivot data
public function products(): BelongsToMany
{
    return $this
        ->belongsToMany(Product::class, 'ec_flash_sale_products', 'flash_sale_id', 'product_id')
        ->withPivot(['price', 'quantity', 'sold']);
}
```

#### Polymorphic Relationships

```php
// Brand can be categorized (polymorphic)
public function categories(): MorphToMany
{
    return $this->morphToMany(
        ProductCategory::class,
        'reference',
        'ec_product_categorizables',
        'reference_id',
        'category_id'
    );
}
```

## 4. Repository Pattern Implementation

### Understanding Repository Pattern

The repository pattern provides:
- Data access abstraction
- Consistent query interface
- Testability
- Business logic separation

### Task 8.4: Examine Repository Interface

1. Look at `platform/core/support/src/Repositories/Interfaces/RepositoryInterface.php`:

```php
interface RepositoryInterface
{
    public function getModel();
    public function setModel(BaseModel|BaseQueryBuilder $model): self;
    public function getTable(): string;
    
    public function all(array $with = []);
    public function findById($id, array $with = []);
    public function findOrFail($id, array $with = []);
    public function getFirstBy(array $condition = [], array $select = [], array $with = []);
    
    public function create(array $data);
    public function createOrUpdate($data, array $condition = []);
    public function update(array $condition, array $data): int;
    public function delete(Model $model): ?bool;
    
    public function count(array $condition = []): int;
    public function pluck(string $column, $key = null, array $condition = []);
    
    // ... more methods
}
```

### Task 8.5: Create Custom Repository

1. Create repository interface:

```php
<?php

namespace Botble\ProductReviews\Repositories\Interfaces;

use Botble\Support\Repositories\Interfaces\RepositoryInterface;

interface ProductReviewInterface extends RepositoryInterface
{
    public function getReviewsByProduct(int $productId, int $limit = 10);
    
    public function getAverageRating(int $productId): float;
    
    public function getTotalReviews(int $productId): int;
    
    public function getReviewsByCustomer(int $customerId, int $limit = 10);
    
    public function getTopRatedProducts(int $limit = 10);
    
    public function getPendingReviews(int $limit = 20);
}
```

2. Create repository implementation:

```php
<?php

namespace Botble\ProductReviews\Repositories;

use Botble\ProductReviews\Repositories\Interfaces\ProductReviewInterface;
use Botble\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductReviewRepository extends RepositoriesAbstract implements ProductReviewInterface
{
    public function getReviewsByProduct(int $productId, int $limit = 10): LengthAwarePaginator
    {
        return $this->model
            ->where('product_id', $productId)
            ->where('status', 'approved')
            ->with(['customer:id,name,avatar'])
            ->latest()
            ->paginate($limit);
    }

    public function getAverageRating(int $productId): float
    {
        return (float) $this->model
            ->where('product_id', $productId)
            ->where('status', 'approved')
            ->avg('rating') ?: 0;
    }

    public function getTotalReviews(int $productId): int
    {
        return $this->model
            ->where('product_id', $productId)
            ->where('status', 'approved')
            ->count();
    }

    public function getReviewsByCustomer(int $customerId, int $limit = 10): LengthAwarePaginator
    {
        return $this->model
            ->where('customer_id', $customerId)
            ->with(['product:id,name,image'])
            ->latest()
            ->paginate($limit);
    }

    public function getTopRatedProducts(int $limit = 10): Collection
    {
        return $this->model
            ->select('product_id')
            ->selectRaw('AVG(rating) as average_rating')
            ->selectRaw('COUNT(*) as review_count')
            ->where('status', 'approved')
            ->groupBy('product_id')
            ->having('review_count', '>=', 5)
            ->orderByDesc('average_rating')
            ->with('product:id,name,image,price')
            ->limit($limit)
            ->get();
    }

    public function getPendingReviews(int $limit = 20): LengthAwarePaginator
    {
        return $this->model
            ->where('status', 'pending')
            ->with(['product:id,name', 'customer:id,name'])
            ->latest()
            ->paginate($limit);
    }
}
```

## 5. Database Migrations

### Migration Best Practices

1. **Descriptive Names**: Use clear, descriptive migration names
2. **Foreign Keys**: Always define proper foreign key constraints
3. **Indexes**: Add indexes for frequently queried columns
4. **Rollback**: Always implement proper down() methods

### Task 8.6: Create Complex Migration

1. Create migration for product reviews with relationships:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')
                ->constrained('ec_products')
                ->onDelete('cascade');
            $table->foreignId('customer_id')
                ->constrained('ec_customers')
                ->onDelete('cascade');
            $table->string('title');
            $table->text('content');
            $table->tinyInteger('rating')->unsigned()->default(5);
            $table->string('status', 60)->default('pending');
            $table->json('images')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')
                ->nullable()
                ->constrained('users')
                ->onDelete('set null');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['product_id', 'status']);
            $table->index(['customer_id', 'status']);
            $table->index(['status', 'created_at']);
            
            // Unique constraint: one review per customer per product
            $table->unique(['product_id', 'customer_id']);
        });

        // Create review statistics table for caching
        Schema::create('product_review_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')
                ->constrained('ec_products')
                ->onDelete('cascade');
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('total_reviews')->default(0);
            $table->integer('five_star')->default(0);
            $table->integer('four_star')->default(0);
            $table->integer('three_star')->default(0);
            $table->integer('two_star')->default(0);
            $table->integer('one_star')->default(0);
            $table->timestamps();
            
            $table->unique('product_id');
            $table->index(['average_rating', 'total_reviews']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_review_stats');
        Schema::dropIfExists('product_reviews');
    }
};
```

## 6. Model Scopes and Query Builders

### Task 8.7: Implement Model Scopes

1. Add scopes to ProductReview model:

```php
class ProductReview extends BaseModel
{
    // Local scopes
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function scopeWithProduct($query)
    {
        return $query->with(['product:id,name,image,price']);
    }

    public function scopeWithCustomer($query)
    {
        return $query->with(['customer:id,name,avatar']);
    }

    // Global scope example
    protected static function booted(): void
    {
        // Automatically exclude deleted products
        static::addGlobalScope('active_product', function ($query) {
            $query->whereHas('product', function ($q) {
                $q->where('status', 'published');
            });
        });
    }
}
```

### Using Scopes

```php
// Using local scopes
$approvedReviews = ProductReview::approved()->withProduct()->get();
$recentHighRated = ProductReview::approved()->byRating(5)->recent(7)->get();

// Chaining multiple scopes
$productReviews = ProductReview::approved()
    ->withProduct()
    ->withCustomer()
    ->recent(30)
    ->orderByDesc('created_at')
    ->paginate(10);
```

## 7. Model Events and Observers

### Task 8.8: Implement Model Events

1. Add model events to ProductReview:

```php
class ProductReview extends BaseModel
{
    protected static function booted(): void
    {
        // Update statistics when review is created
        static::created(function (ProductReview $review) {
            if ($review->status === 'approved') {
                static::updateProductStats($review->product_id);
            }
        });

        // Update statistics when review is updated
        static::updated(function (ProductReview $review) {
            if ($review->wasChanged('status') || $review->wasChanged('rating')) {
                static::updateProductStats($review->product_id);
            }
        });

        // Update statistics when review is deleted
        static::deleted(function (ProductReview $review) {
            static::updateProductStats($review->product_id);
        });
    }

    protected static function updateProductStats(int $productId): void
    {
        $stats = static::where('product_id', $productId)
            ->where('status', 'approved')
            ->selectRaw('
                AVG(rating) as average_rating,
                COUNT(*) as total_reviews,
                SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
                SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
                SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
                SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
                SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
            ')
            ->first();

        ProductReviewStats::updateOrCreate(
            ['product_id' => $productId],
            [
                'average_rating' => round($stats->average_rating ?? 0, 2),
                'total_reviews' => $stats->total_reviews ?? 0,
                'five_star' => $stats->five_star ?? 0,
                'four_star' => $stats->four_star ?? 0,
                'three_star' => $stats->three_star ?? 0,
                'two_star' => $stats->two_star ?? 0,
                'one_star' => $stats->one_star ?? 0,
            ]
        );
    }
}
```

## 8. Caching Strategies

### Task 8.9: Implement Model Caching

1. Add caching to repository methods:

```php
class ProductReviewRepository extends RepositoriesAbstract implements ProductReviewInterface
{
    public function getAverageRating(int $productId): float
    {
        return Cache::remember(
            "product_review_average_{$productId}",
            3600, // 1 hour
            function () use ($productId) {
                return (float) $this->model
                    ->where('product_id', $productId)
                    ->where('status', 'approved')
                    ->avg('rating') ?: 0;
            }
        );
    }

    public function getTotalReviews(int $productId): int
    {
        return Cache::remember(
            "product_review_count_{$productId}",
            3600,
            function () use ($productId) {
                return $this->model
                    ->where('product_id', $productId)
                    ->where('status', 'approved')
                    ->count();
            }
        );
    }

    public function getTopRatedProducts(int $limit = 10): Collection
    {
        return Cache::remember(
            "top_rated_products_{$limit}",
            7200, // 2 hours
            function () use ($limit) {
                return $this->model
                    ->select('product_id')
                    ->selectRaw('AVG(rating) as average_rating')
                    ->selectRaw('COUNT(*) as review_count')
                    ->where('status', 'approved')
                    ->groupBy('product_id')
                    ->having('review_count', '>=', 5)
                    ->orderByDesc('average_rating')
                    ->with('product:id,name,image,price')
                    ->limit($limit)
                    ->get();
            }
        );
    }

    // Clear cache when reviews change
    public function create(array $data)
    {
        $review = parent::create($data);
        
        $this->clearProductCache($review->product_id);
        
        return $review;
    }

    protected function clearProductCache(int $productId): void
    {
        Cache::forget("product_review_average_{$productId}");
        Cache::forget("product_review_count_{$productId}");
        Cache::forget("top_rated_products_10");
        Cache::forget("top_rated_products_20");
    }
}
```

## 9. Query Optimization

### Performance Best Practices

1. **Eager Loading**: Use `with()` to prevent N+1 queries
2. **Select Specific Columns**: Only select needed columns
3. **Indexes**: Add database indexes for frequently queried columns
4. **Chunking**: Use `chunk()` for large datasets
5. **Caching**: Cache expensive queries

### Task 8.10: Optimize Queries

```php
// Bad: N+1 query problem
$reviews = ProductReview::all();
foreach ($reviews as $review) {
    echo $review->product->name; // Triggers additional query
    echo $review->customer->name; // Triggers additional query
}

// Good: Eager loading
$reviews = ProductReview::with(['product:id,name', 'customer:id,name'])->get();
foreach ($reviews as $review) {
    echo $review->product->name; // No additional query
    echo $review->customer->name; // No additional query
}

// Good: Select only needed columns
$reviews = ProductReview::select(['id', 'product_id', 'customer_id', 'rating', 'title'])
    ->with([
        'product:id,name,image',
        'customer:id,name,avatar'
    ])
    ->approved()
    ->latest()
    ->paginate(20);

// Good: Use chunking for large datasets
ProductReview::chunk(1000, function ($reviews) {
    foreach ($reviews as $review) {
        // Process review
    }
});
```

## 10. Practice Assignment

### Assignment 8.1: Create Review Statistics Model

1. Create ProductReviewStats model with relationships
2. Implement automatic statistics updates
3. Add caching for statistics queries

### Assignment 8.2: Optimize Product Queries

1. Analyze and optimize product listing queries
2. Implement proper eager loading
3. Add database indexes for performance

### Assignment 8.3: Advanced Repository

1. Create advanced repository methods
2. Implement query caching
3. Add complex filtering capabilities

## 11. Key Takeaways

1. **BaseModel**: All models extend BaseModel for consistent functionality
2. **Relationships**: Properly define and use Eloquent relationships
3. **Repository Pattern**: Abstracts data access and improves testability
4. **Migrations**: Use proper constraints, indexes, and rollback methods
5. **Scopes**: Encapsulate common query logic in model scopes
6. **Events**: Use model events for automatic data maintenance
7. **Caching**: Implement caching for expensive queries
8. **Optimization**: Always consider query performance and N+1 problems

## 12. Next Steps

In Lesson 9, we'll explore advanced customization with a case study:
- Vendor dashboard UI modifications
- Custom marketplace features
- Advanced theme customization
- Performance optimization

## Quick Reference

### Model Relationships
```php
// One-to-Many
public function items(): HasMany
{
    return $this->hasMany(Item::class);
}

// Many-to-Many
public function categories(): BelongsToMany
{
    return $this->belongsToMany(Category::class);
}

// Polymorphic
public function comments(): MorphMany
{
    return $this->morphMany(Comment::class, 'commentable');
}
```

### Repository Pattern
```php
// Interface
interface ItemInterface extends RepositoryInterface
{
    public function getByCategory(int $categoryId);
}

// Implementation
class ItemRepository extends RepositoriesAbstract implements ItemInterface
{
    public function getByCategory(int $categoryId)
    {
        return $this->model->where('category_id', $categoryId)->get();
    }
}
```
