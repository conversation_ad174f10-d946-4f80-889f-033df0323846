# Martfury Multi-Vendor Marketplace Crash Course

Welcome to the comprehensive 10-lesson crash course for the Martfury Multi-Vendor Marketplace built on Botble CMS. This course is designed for Laravel beginners with basic PHP knowledge who want to master the Martfury marketplace system.

## Course Overview

This crash course covers everything you need to know to customize, extend, and maintain the Martfury Multi-Vendor Marketplace. Each lesson builds upon the previous one, providing hands-on experience with real code examples from the actual codebase.

## Prerequisites

- Basic PHP knowledge
- Familiarity with HTML/CSS
- Understanding of MVC concepts
- Local development environment (Laragon, XAMPP, or similar)

## Course Structure

### Lesson 1: Botble CMS Fundamentals and Martfury Architecture
- **Duration**: 2-3 hours
- **Focus**: Understanding the foundation of Botble CMS and Martfury theme structure
- **Key Topics**: Directory structure, modular architecture, theme basics

### Lesson 2: Theme Customization Essentials
- **Duration**: 2-3 hours
- **Focus**: Blade templates, assets, and navigation customization
- **Key Topics**: Template hierarchy, asset compilation, menu systems

### Lesson 3: Plugin System Deep-dive
- **Duration**: 3-4 hours
- **Focus**: Understanding plugin architecture and marketplace functionality
- **Key Topics**: Plugin structure, marketplace logic, vendor management

### Lesson 4: Routing and Controller Patterns
- **Duration**: 2-3 hours
- **Focus**: URL routing, controller implementation, and request handling
- **Key Topics**: Route definitions, controller methods, middleware

### Lesson 5: Custom Plugin Development
- **Duration**: 4-5 hours
- **Focus**: Building a complete plugin from scratch
- **Key Topics**: Plugin creation, CRUD operations, service providers

### Lesson 6: Admin Panel Customization
- **Duration**: 3-4 hours
- **Focus**: Modifying the admin interface and dashboard
- **Key Topics**: Admin menus, widgets, custom sections

### Lesson 7: Access Control Lists (ACL)
- **Duration**: 3-4 hours
- **Focus**: User roles, permissions, and security
- **Key Topics**: Role management, permission systems, vendor access

### Lesson 8: Data Layer Management
- **Duration**: 3-4 hours
- **Focus**: Models, repositories, and database interactions
- **Key Topics**: Eloquent models, repository pattern, migrations

### Lesson 9: Advanced Customization Case Study
- **Duration**: 4-5 hours
- **Focus**: Vendor dashboard modifications and marketplace features
- **Key Topics**: UI customization, vendor-specific functionality

### Lesson 10: Capstone Project
- **Duration**: 5-6 hours
- **Focus**: Building a complete reviews/ratings module
- **Key Topics**: Full-stack development, integration, testing

## Learning Objectives

By the end of this course, you will be able to:

1. Navigate and understand the Botble CMS architecture
2. Customize Martfury theme templates and styling
3. Develop custom plugins for marketplace functionality
4. Modify routing and controller logic
5. Implement custom admin panel features
6. Manage user roles and permissions effectively
7. Work with the data layer and database relationships
8. Build complete marketplace features from scratch

## Getting Started

1. Ensure your Martfury installation is working properly
2. Set up your development environment
3. Familiarize yourself with the project structure
4. Start with Lesson 1 and progress sequentially

## Support and Resources

- **Official Botble Documentation**: https://docs.botble.com/
- **Laravel Documentation**: https://laravel.com/docs
- **Martfury Theme Files**: `platform/themes/martfury/`
- **Plugin Examples**: `platform/plugins/`

## Course Files

Each lesson includes:
- Detailed explanations and concepts
- Hands-on tasks with specific file paths
- Code examples with syntax highlighting
- Practice assignments to reinforce learning

Let's begin your journey to mastering the Martfury Multi-Vendor Marketplace!
