@if (get_payment_setting('status', TABBY_PAYMENT_METHOD_NAME) == 1)
    @php
        $paymentService = new Botble\Tabby\Services\Gateways\TabbyPaymentService();
    @endphp

    <x-plugins-payment::payment-method
        :name="TABBY_PAYMENT_METHOD_NAME"
        paymentName="Tabby Pay-in-4"
        :supportedCurrencies="$paymentService->supportedCurrencyCodes()"
    >
        <x-slot name="currencyNotSupportedMessage">
            <p class="mt-1 mb-0">
                {{ __('Learn more') }}:
                {{ Html::link('https://tabby.ai/en/supported-countries/', attributes: ['target' => '_blank', 'rel' => 'nofollow']) }}.
            </p>
        </x-slot>

        @if ($errorMessage)
            <div class="text-danger my-2">
                {!! BaseHelper::clean($errorMessage) !!}
            </div>
        @endif

        @if (!empty($rejectionReason) && !$isEligible)
            <div class="alert alert-warning my-2">
                <i class="fas fa-exclamation-triangle"></i>
                {{ $rejectionMessage }}
            </div>
        @elseif ($isEligible)
            <div class="tabby-payment-info my-2">
                <div class="d-flex align-items-center mb-2">
                    <img src="{{ url('vendor/core/plugins/tabby/images/tabby.svg') }}" alt="Tabby" style="height: 24px; margin-right: 8px;">
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i>
                        {{ __('Pay in 4 installments with Tabby') }}
                    </span>
                </div>
                <div class="text-muted small">
                    {{ __('Split your purchase into 4 interest-free payments. No fees when you pay on time.') }}
                </div>
                <div class="tabby-card" id="tabbyCard"></div>
            </div>
        @endif
    </x-plugins-payment::payment-method>
@endif

