# Lesson 7: Access Control Lists (ACL)

## Learning Objectives

By the end of this lesson, you will understand:
- User roles and permissions system architecture
- Permission checking mechanisms and middleware
- Vendor access control in marketplace
- Custom permission creation and management
- Role-based access control implementation
- Security best practices
- Multi-vendor permission isolation
- API authentication and authorization

## 1. ACL System Architecture

### Core Components

Botble CMS implements a comprehensive ACL system with three main components:

1. **Users**: Individual accounts with specific permissions
2. **Roles**: Collections of permissions that can be assigned to users
3. **Permissions**: Specific actions that can be performed

### Database Structure

```sql
-- Users table
users (id, username, email, password, permissions, super_user, ...)

-- Roles table  
roles (id, name, slug, permissions, description, is_default, ...)

-- Role-User pivot table
role_users (user_id, role_id, created_at, updated_at)
```

### Permission Storage

Permissions are stored in two ways:
- **Configuration Files**: Define available permissions
- **Database**: Store user/role permission assignments

## 2. Understanding Permissions

### Task 7.1: Examine Core Permissions

1. Look at `platform/core/acl/config/permissions.php`:

```php
return [
    [
        'name' => 'Users',
        'flag' => 'users.index',
        'parent_flag' => 'core.system',
    ],
    [
        'name' => 'Create',
        'flag' => 'users.create',
        'parent_flag' => 'users.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'users.edit',
        'parent_flag' => 'users.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'users.destroy',
        'parent_flag' => 'users.index',
    ],
];
```

### Permission Structure

Each permission has:
- **name**: Human-readable display name
- **flag**: Unique identifier for programmatic checking
- **parent_flag**: Optional parent for hierarchical organization

### Task 7.2: Examine Marketplace Permissions

1. Look at `platform/plugins/marketplace/config/permissions.php`:

```php
return [
    [
        'name' => 'Marketplace',
        'flag' => 'marketplace.index',
    ],
    [
        'name' => 'Stores',
        'flag' => 'marketplace.store.index',
        'parent_flag' => 'marketplace.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'marketplace.store.create',
        'parent_flag' => 'marketplace.store.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'marketplace.store.edit',
        'parent_flag' => 'marketplace.store.index',
    ],
    [
        'name' => 'Vendors',
        'flag' => 'marketplace.vendors.index',
        'parent_flag' => 'marketplace.index',
    ],
    [
        'name' => 'Block/Unblock',
        'flag' => 'marketplace.vendors.control',
        'parent_flag' => 'marketplace.vendors.index',
    ],
];
```

## 3. User Model and Permission Checking

### Task 7.3: Examine User Model

1. Look at `platform/core/acl/src/Models/User.php`:

```php
class User extends BaseModel
{
    use PermissionTrait;

    protected $casts = [
        'permissions' => 'json',
        // ...
    ];

    public function roles(): BelongsToMany
    {
        return $this
            ->belongsToMany(Role::class, 'role_users', 'user_id', 'role_id')
            ->withTimestamps();
    }

    public function isSuperUser(): bool
    {
        return $this->super_user || $this->traitHasPermission(ACL_ROLE_SUPER_USER);
    }

    public function hasPermission(string|array $permissions): bool
    {
        if ($this->isSuperUser()) {
            return true;
        }

        return $this->traitHasPermission($permissions);
    }

    public function hasAnyPermission(string|array $permissions): bool
    {
        if ($this->isSuperUser()) {
            return true;
        }

        return $this->traitHasAnyPermission($permissions);
    }
}
```

### Permission Checking Methods

- `hasPermission($permission)`: Check if user has specific permission
- `hasAnyPermission($permissions)`: Check if user has any of the permissions
- `isSuperUser()`: Check if user is a super user (has all permissions)

## 4. Role Model and Management

### Task 7.4: Examine Role Model

1. Look at `platform/core/acl/src/Models/Role.php`:

```php
class Role extends BaseModel
{
    use PermissionTrait;

    protected $fillable = [
        'name',
        'slug',
        'permissions',
        'description',
        'is_default',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'permissions' => 'json',
        'is_default' => 'bool',
    ];

    public function users(): BelongsToMany
    {
        return $this
            ->belongsToMany(User::class, 'role_users', 'role_id', 'user_id')
            ->withTimestamps();
    }

    public function getAvailablePermissions(): array
    {
        $permissions = [];
        $types = ['core', 'packages', 'plugins'];

        foreach ($types as $type) {
            foreach (BaseHelper::scanFolder(platform_path($type)) as $module) {
                $configuration = config(strtolower($type . '.' . $module . '.permissions'));
                if (! empty($configuration)) {
                    foreach ($configuration as $config) {
                        $permissions[$config['flag']] = $config;
                    }
                }
            }
        }

        return apply_filters('core_acl_role_permissions', $permissions);
    }
}
```

## 5. Middleware and Route Protection

### Task 7.5: Examine Authentication Middleware

1. Look at `platform/core/acl/src/Http/Middleware/Authenticate.php`:

```php
class Authenticate extends BaseAuthenticate
{
    public function handle($request, Closure $next, ...$guards)
    {
        $this->authenticate($request, $guards);

        if (! $guards) {
            $route = $request->route();
            $flag = $route->getAction('permission');
            if ($flag === null) {
                $flag = $route->getName();
            }

            // Clean up route names for permission checking
            $flag = preg_replace('/.create.store$/', '.create', $flag);
            $flag = preg_replace('/.edit.update$/', '.edit', $flag);

            if ($flag && ! $request->user()->hasAnyPermission((array) $flag)) {
                if ($request->expectsJson()) {
                    return response()->json(['message' => 'Unauthenticated.'], 401);
                }

                return redirect()->route('dashboard.index');
            }
        }

        return $next($request);
    }
}
```

### Automatic Permission Checking

The middleware automatically:
1. Extracts permission flag from route name
2. Cleans up common route patterns
3. Checks if user has required permission
4. Redirects unauthorized users

## 6. Vendor Access Control

### Task 7.6: Examine Vendor Middleware

1. Look at `platform/plugins/marketplace/src/Http/Middleware/RedirectIfNotVendor.php`:

```php
class RedirectIfNotVendor
{
    public function handle(Request $request, Closure $next, string $guard = 'customer')
    {
        if (! Auth::guard($guard)->check() || ! Auth::guard($guard)->user()->is_vendor) {
            if ($request->ajax() || $request->wantsJson()) {
                return response('Unauthorized.', 401);
            }

            return redirect()->guest(route('customer.login'));
        }

        if (MarketplaceHelper::getSetting('verify_vendor', true) &&
            ! Auth::guard($guard)->user()->vendor_verified_at) {
            if ($request->ajax() || $request->wantsJson()) {
                return response(__('Vendor account is not verified.'), 403);
            }

            return redirect()->guest(route('marketplace.vendor.become-vendor'));
        }

        return $next($request);
    }
}
```

### Vendor Access Levels

1. **Customer**: Basic customer account
2. **Vendor**: Customer with vendor privileges
3. **Verified Vendor**: Vendor with verified status
4. **Admin**: Full administrative access

## 7. Creating Custom Permissions

### Task 7.7: Add Custom Permissions

1. Create permissions for your plugin in `config/permissions.php`:

```php
<?php

return [
    [
        'name' => 'Product Reviews',
        'flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'product-reviews.create',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'product-reviews.edit',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'product-reviews.destroy',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Approve',
        'flag' => 'product-reviews.approve',
        'parent_flag' => 'product-reviews.index',
    ],
    [
        'name' => 'Moderate',
        'flag' => 'product-reviews.moderate',
        'parent_flag' => 'product-reviews.index',
    ],
];
```

2. Update permissions database:

```bash
php artisan cms:user:rebuild-permissions
```

## 8. Route Permission Configuration

### Task 7.8: Configure Route Permissions

1. Explicit permission specification:

```php
Route::get('reviews', [
    'as' => 'product-reviews.index',
    'uses' => 'ProductReviewController@index',
    'permission' => 'product-reviews.index',
]);

Route::post('reviews/{id}/approve', [
    'as' => 'product-reviews.approve',
    'uses' => 'ProductReviewController@approve',
    'permission' => 'product-reviews.approve',
]);
```

2. Automatic permission from route name:

```php
// Will automatically check for 'product-reviews.create' permission
Route::get('reviews/create', [
    'as' => 'product-reviews.create',
    'uses' => 'ProductReviewController@create',
]);
```

## 9. Permission Checking in Controllers

### Task 7.9: Implement Permission Checks

1. In controller methods:

```php
class ProductReviewController extends BaseController
{
    public function approve(int $id, BaseHttpResponse $response)
    {
        // Check permission explicitly
        if (! auth()->user()->hasPermission('product-reviews.approve')) {
            return $response->setError()->setMessage('Unauthorized');
        }

        $review = $this->productReviewRepository->findOrFail($id);
        
        $review->update([
            'status' => ReviewStatusEnum::APPROVED,
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        return $response->withUpdatedSuccessMessage();
    }

    public function bulkModerate(Request $request, BaseHttpResponse $response)
    {
        // Check multiple permissions
        if (! auth()->user()->hasAnyPermission(['product-reviews.approve', 'product-reviews.moderate'])) {
            return $response->setError()->setMessage('Insufficient permissions');
        }

        // Bulk moderation logic...
    }
}
```

## 10. Frontend Permission Checking

### Task 7.10: Check Permissions in Views

1. In Blade templates:

```blade
@if(auth()->check() && auth()->user()->hasPermission('product-reviews.create'))
    <a href="{{ route('product-reviews.create') }}" class="btn btn-primary">
        Add Review
    </a>
@endif

@if(auth()->check() && auth()->user()->hasAnyPermission(['product-reviews.approve', 'product-reviews.moderate']))
    <div class="moderation-tools">
        <button class="btn btn-success" onclick="approveReview({{ $review->id }})">
            Approve
        </button>
        <button class="btn btn-danger" onclick="rejectReview({{ $review->id }})">
            Reject
        </button>
    </div>
@endif
```

2. In menu registration:

```php
DashboardMenuItem::make()
    ->id('cms-plugins-product-reviews')
    ->name('Product Reviews')
    ->route('product-reviews.index')
    ->permissions(['product-reviews.index']) // Only show if user has permission
```

## 11. Creating Custom Middleware

### Task 7.11: Create Permission Middleware

1. Create custom middleware:

```php
<?php

namespace Botble\ProductReviews\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckReviewModerationPermission
{
    public function handle(Request $request, Closure $next)
    {
        if (! Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        
        if (! $user->hasAnyPermission(['product-reviews.approve', 'product-reviews.moderate'])) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Insufficient permissions'], 403);
            }

            return redirect()->route('dashboard.index')
                ->with('error', 'You do not have permission to moderate reviews.');
        }

        return $next($request);
    }
}
```

2. Register middleware in service provider:

```php
public function boot(): void
{
    $this->app['router']->aliasMiddleware('review.moderate', CheckReviewModerationPermission::class);
}
```

3. Use in routes:

```php
Route::group(['middleware' => 'review.moderate'], function () {
    Route::post('reviews/{id}/approve', 'ProductReviewController@approve');
    Route::post('reviews/{id}/reject', 'ProductReviewController@reject');
});
```

## 12. Role Management

### Task 7.12: Create and Manage Roles

1. Create roles programmatically:

```php
use Botble\ACL\Models\Role;

// Create Review Moderator role
$moderatorRole = Role::create([
    'name' => 'Review Moderator',
    'description' => 'Can moderate product reviews',
    'permissions' => [
        'product-reviews.index' => true,
        'product-reviews.approve' => true,
        'product-reviews.moderate' => true,
        'product-reviews.edit' => true,
    ],
]);

// Create Review Manager role
$managerRole = Role::create([
    'name' => 'Review Manager',
    'description' => 'Full review management access',
    'permissions' => [
        'product-reviews.index' => true,
        'product-reviews.create' => true,
        'product-reviews.edit' => true,
        'product-reviews.destroy' => true,
        'product-reviews.approve' => true,
        'product-reviews.moderate' => true,
    ],
]);
```

2. Assign roles to users:

```php
use Botble\ACL\Models\User;

$user = User::find(1);
$user->roles()->sync([$moderatorRole->id]);

// Or assign multiple roles
$user->roles()->sync([$moderatorRole->id, $managerRole->id]);
```

## 13. Security Best Practices

### Permission Design Principles

1. **Principle of Least Privilege**: Grant minimum permissions needed
2. **Role-Based Access**: Use roles instead of direct user permissions
3. **Hierarchical Permissions**: Organize permissions in logical hierarchies
4. **Regular Audits**: Review and update permissions regularly

### Common Security Patterns

```php
// Always check permissions before sensitive operations
public function deleteReview(int $id)
{
    if (! auth()->user()->hasPermission('product-reviews.destroy')) {
        abort(403);
    }
    
    // Deletion logic...
}

// Use middleware for route groups
Route::group(['middleware' => 'auth', 'permission' => 'admin.access'], function () {
    // Admin routes
});

// Validate ownership for user-specific resources
public function editReview(int $id)
{
    $review = ProductReview::findOrFail($id);
    
    if ($review->customer_id !== auth()->id() && ! auth()->user()->hasPermission('product-reviews.edit')) {
        abort(403);
    }
    
    // Edit logic...
}
```

## 14. Practice Assignment

### Assignment 7.1: Implement Review Permissions

1. Create comprehensive permission system for product reviews
2. Implement role-based access for different user types
3. Add middleware for route protection

### Assignment 7.2: Vendor Permission Isolation

1. Ensure vendors can only access their own data
2. Implement proper permission checks for vendor operations
3. Create vendor-specific permission middleware

### Assignment 7.3: Advanced Security

1. Implement permission caching for performance
2. Add audit logging for permission changes
3. Create permission testing suite

## 15. Key Takeaways

1. **Hierarchical Permissions**: Organize permissions in logical parent-child relationships
2. **Automatic Checking**: Middleware automatically checks permissions based on route names
3. **Multiple Guards**: Support for different authentication guards (admin, customer, vendor)
4. **Role-Based Access**: Use roles to group permissions logically
5. **Security First**: Always check permissions before sensitive operations
6. **Performance**: Consider caching for frequently checked permissions

## 16. Next Steps

In Lesson 8, we'll explore the data layer:
- Models and relationships
- Repository pattern implementation
- Database migrations and schema design
- Query optimization and performance

## Quick Reference

### Permission Checking
```php
// Check single permission
auth()->user()->hasPermission('permission.flag')

// Check multiple permissions (AND)
auth()->user()->hasPermission(['perm1', 'perm2'])

// Check any permission (OR)
auth()->user()->hasAnyPermission(['perm1', 'perm2'])

// Check if super user
auth()->user()->isSuperUser()
```

### Route Protection
```php
// Explicit permission
Route::get('path', ['permission' => 'flag', 'uses' => 'Controller@method']);

// Middleware
Route::group(['middleware' => 'auth'], function () {
    // Protected routes
});
```
