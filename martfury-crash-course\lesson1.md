# Lesson 1: Botble CMS Fundamentals and Martfury Architecture

## Learning Objectives

By the end of this lesson, you will understand:
- The modular architecture of Botble CMS
- Directory structure and organization
- How themes integrate with the core system
- Martfury theme architecture and components
- Asset management and compilation process

## 1. Botble CMS Architecture Overview

### What is Botble CMS?

Botble CMS is a modern PHP platform built on Laravel Framework 12.x, designed with a modular architecture that separates functionality into distinct, reusable components. This approach provides:

- **Separation of Concerns**: Each module handles specific functionality
- **Reusability**: Modules can be reused across different projects
- **Maintainability**: Changes to one module don't affect others
- **Scalability**: New features can be added as new modules
- **Testability**: Modules can be tested in isolation

### Core Architecture Principles

1. **Modular Design**: Everything is organized into modules (core, packages, plugins, themes)
2. **Service Provider Pattern**: <PERSON><PERSON>'s service provider pattern for module loading
3. **Event-Driven**: Hooks and events for extensibility
4. **Repository Pattern**: Data access abstraction layer

## 2. Directory Structure Deep Dive

### Root Directory Structure

```
martfury/
├── app/                    # Laravel application files
├── bootstrap/              # Laravel bootstrap files
├── config/                 # Laravel configuration files
├── database/               # Database migrations and seeders
├── platform/               # Botble CMS core (THE HEART OF THE SYSTEM)
│   ├── core/              # Core modules
│   ├── packages/          # Reusable packages
│   ├── plugins/           # Feature-specific plugins
│   └── themes/            # Frontend themes
├── public/                 # Web-accessible files
├── resources/              # Laravel resources (views, lang, etc.)
├── routes/                 # Laravel route definitions
├── storage/                # Laravel storage
└── vendor/                 # Composer dependencies
```

### The Platform Directory - Core of Botble CMS

The `/platform` directory contains all Botble CMS-specific code:

#### Core Modules (`platform/core/`)

```
platform/core/
├── acl/                   # Access Control Lists (users, roles, permissions)
├── base/                  # Base functionality and helpers
├── dashboard/             # Admin dashboard
├── media/                 # Media management
├── setting/               # System settings
├── support/               # Support utilities
├── table/                 # Data table components
├── js-validation/         # JavaScript validation
├── chart/                 # Chart components
└── icon/                  # Icon management
```

#### Packages (`platform/packages/`)

```
platform/packages/
├── get-started/           # Getting started wizard
├── installer/             # Installation package
├── menu/                  # Menu management
├── optimize/              # Performance optimization
├── page/                  # Static pages
├── plugin-management/     # Plugin management
├── revision/              # Content revision system
├── seo-helper/            # SEO optimization
├── shortcode/             # Shortcode system
├── sitemap/               # XML sitemap generation
├── slug/                  # URL slug management
├── theme/                 # Theme management
└── widget/                # Widget system
```

#### Plugins (`platform/plugins/`)

Key plugins for Martfury marketplace:

```
platform/plugins/
├── ecommerce/             # E-commerce functionality
├── marketplace/           # Multi-vendor marketplace
├── payment/               # Payment processing
├── location/              # Location management
├── blog/                  # Blog functionality
├── contact/               # Contact forms
├── newsletter/            # Newsletter management
└── [other plugins...]
```

## 3. Hands-on Task: Exploring the Directory Structure

### Task 1.1: Navigate the Core Structure

1. Open your file explorer and navigate to the project root
2. Examine the `platform/core/` directory
3. Look at the `platform/core/composer.json` file:

```bash
# Path: platform/core/composer.json
```

**What you'll find**: This file shows how core modules are autoloaded using PSR-4 standards.

### Task 1.2: Examine Core Configuration

1. Navigate to `platform/core/base/config/general.php`
2. Examine the configuration structure:

```php
// Key configurations you'll find:
'admin_dir' => env('ADMIN_DIR', 'admin'),
'base_name' => env('APP_NAME', 'Botble Technologies'),
'logo' => '/vendor/core/core/base/images/logo.png',
'favicon' => '/vendor/core/core/base/images/favicon.png',
```

**Understanding**: This file controls core CMS settings like admin directory, branding, and editor preferences.

### Task 1.3: Explore the Service Provider Pattern

1. Open `platform/core/base/src/Providers/BaseServiceProvider.php`
2. Look at the `boot()` method around line 99:

```php
public function boot(): void
{
    $this
        ->loadAndPublishConfigurations(['permissions', 'assets'])
        ->loadAndPublishViews()
        ->loadAnonymousComponents()
        ->loadAndPublishTranslations()
        ->loadRoutes()
        ->loadMigrations()
        ->publishAssets();
}
```

**Understanding**: This shows how Botble CMS modules are loaded and initialized using Laravel's service provider pattern.

## 4. Martfury Theme Architecture

### Theme Structure Overview

```
platform/themes/martfury/
├── assets/                # Source assets (SASS, JS)
│   ├── js/               # JavaScript source files
│   └── sass/             # SASS source files
├── config.php            # Theme configuration and events
├── functions/            # Theme functions and customizations
├── layouts/              # Page layouts (default, homepage, etc.)
├── partials/             # Reusable template parts
├── public/               # Compiled assets (CSS, JS)
├── resources/            # Additional resources
├── routes/               # Theme-specific routes
├── src/                  # PHP classes and controllers
├── views/                # Blade templates
├── widgets/              # Custom widgets
├── theme.json            # Theme metadata
└── webpack.mix.js        # Asset compilation configuration
```

### Task 1.4: Examine Martfury Theme Configuration

1. Open `platform/themes/martfury/theme.json`:

```json
{
    "name": "Martfury",
    "namespace": "Theme\\Martfury\\",
    "description": "Martfury - Laravel Ecommerce System",
    "author": "Botble Technologies",
    "required_plugins": []
}
```

2. Open `platform/themes/martfury/config.php` and examine the structure:

```php
// Look for the 'events' array which defines theme lifecycle events
'events' => [
    'beforeRenderTheme' => function($theme) {
        // Asset loading and theme setup
    },
    'beforeRenderLayout' => [
        'default' => function($theme) {
            // Layout-specific setup
        }
    ]
]
```

**Understanding**: The config.php file is where themes register assets, set up event listeners, and configure theme behavior.

## 5. Asset Management System

### How Assets Work in Botble CMS

Botble CMS uses a sophisticated asset management system:

1. **Source Assets**: Located in `platform/themes/martfury/assets/`
2. **Compilation**: Using Laravel Mix (webpack.mix.js)
3. **Public Assets**: Compiled to `public/themes/martfury/`
4. **Theme Assets**: Also copied to `platform/themes/martfury/public/`

### Task 1.5: Understand Asset Compilation

1. Examine `platform/themes/martfury/webpack.mix.js`:

```javascript
let mix = require('laravel-mix')
const path = require('path')
let directory = path.basename(path.resolve(__dirname))

const source = 'platform/themes/' + directory
const dist = 'public/themes/' + directory

mix
    .sass(source + '/assets/sass/style.scss', dist + '/css')
    .sass(source + '/assets/sass/rtl.scss', dist + '/css')
    .js(source + '/assets/js/main.js', dist + '/js')
    .js(source + '/assets/js/backend.js', dist + '/js')
```

**Understanding**: This configuration compiles SASS to CSS and bundles JavaScript files.

2. Check the asset registration in `platform/themes/martfury/config.php`:

```php
// Around line 84-90, you'll find:
$theme->asset()->container('footer')->usePath()
    ->add('masonry-js', 'plugins/masonry.pkgd.min.js', ['jquery']);

$theme->asset()->container('footer')->usePath()
    ->add('main', 'js/main.js', ['jquery'], [], $version);
```

**Understanding**: This shows how themes register and load assets with dependencies.

## 6. Theme Integration with Core System

### How Themes Connect to Botble CMS

1. **Theme Registration**: Through theme.json and service providers
2. **Asset Publishing**: Assets are published to public directory
3. **View Resolution**: Blade templates are resolved through theme system
4. **Event Hooks**: Themes can hook into core events

### Task 1.6: Explore Theme Functions

1. Open `platform/themes/martfury/functions/functions.php`:

```php
// You'll find theme registration functions:
register_page_template([
    'blog-sidebar' => __('Blog Sidebar'),
    'full-width' => __('Full width'),
    'homepage' => __('Homepage'),
    'coming-soon' => __('Coming soon'),
]);

register_sidebar([
    'id' => 'footer_sidebar',
    'name' => __('Footer sidebar'),
    'description' => __('Widgets in footer of page'),
]);
```

**Understanding**: This file registers page templates and widget areas that can be used throughout the theme.

## 7. Code Examples and Best Practices

### Example 1: Basic Theme Usage

```php
// In a controller
use Theme;

class HomeController extends Controller {
    public function getIndex()
    {
        $theme = Theme::uses('martfury')->layout('homepage');
        
        $view = [
            'featured_products' => $this->getFeaturedProducts(),
            'categories' => $this->getCategories(),
        ];

        return $theme->scope('index', $view)->render();
    }
}
```

### Example 2: Adding Custom Assets

```php
// In theme config.php
'beforeRenderTheme' => function($theme) {
    $theme->asset()->usePath()->add('custom-style', 'css/custom.css');
    $theme->asset()->container('footer')->usePath()
        ->add('custom-script', 'js/custom.js', ['jquery']);
}
```

## 8. Practice Assignment

### Assignment 1.1: Create a Custom Page Template

1. Navigate to `platform/themes/martfury/functions/functions.php`
2. Add a new page template registration:

```php
register_page_template([
    'custom-landing' => __('Custom Landing Page'),
]);
```

3. Create the corresponding layout file:
   - Create `platform/themes/martfury/layouts/custom-landing.blade.php`
   - Copy content from `default.blade.php` and modify as needed

### Assignment 1.2: Explore Asset Loading

1. Open your browser's developer tools
2. Navigate to your Martfury site
3. Check the Network tab to see which assets are loaded
4. Identify which assets come from:
   - Core (`/vendor/core/`)
   - Theme (`/themes/martfury/`)
   - Plugins (`/vendor/core/plugins/`)

## 9. Key Takeaways

1. **Modular Architecture**: Botble CMS separates functionality into core, packages, plugins, and themes
2. **Service Providers**: Laravel's service provider pattern is used throughout for module loading
3. **Asset Management**: Sophisticated system for compiling and serving assets
4. **Theme Integration**: Themes integrate deeply with the core system through events and configuration
5. **File Organization**: Clear separation between source files and compiled/public assets

## 10. Next Steps

In Lesson 2, we'll dive deeper into theme customization, focusing on:
- Blade template system and hierarchy
- Customizing layouts and partials
- Working with theme options
- Managing navigation menus
- Responsive design implementation

## Quick Reference

### Important File Paths
- Core configuration: `platform/core/base/config/general.php`
- Theme metadata: `platform/themes/martfury/theme.json`
- Theme configuration: `platform/themes/martfury/config.php`
- Theme functions: `platform/themes/martfury/functions/functions.php`
- Asset compilation: `platform/themes/martfury/webpack.mix.js`

### Key Commands
```bash
# Publish theme assets
php artisan cms:theme:assets:publish martfury

# Create new theme
php artisan cms:theme:create <theme-name>

# Activate theme
php artisan cms:theme:activate <theme-name>
```
